# Symfony Process 组件技术深度分析

## 1. Symfony Process 组件核心原理

### 1.1 架构设计概览

```php
// Symfony Process 核心架构
namespace Symfony\Component\Process;

class Process
{
    private $commandline;           // 命令行字符串
    private $cwd;                  // 工作目录
    private $env;                  // 环境变量
    private $input;                // 标准输入
    private $starttime;            // 启动时间
    private $lastOutputTime;       // 最后输出时间
    private $timeout;              // 超时时间
    private $idleTimeout;          // 空闲超时
    private $exitcode;             // 退出码
    private $fallbackStatus;       // 回退状态
    private $processInformation;   // 进程信息
    private $outputDisabled = false;
    private $pipes;                // 管道
    private $process;              // 进程资源
    private $status = self::STATUS_READY;
    private $incrementalOutputOffset = 0;
    private $incrementalErrorOutputOffset = 0;
    private $tty = false;
    private $pty = true;
    private $inheritEnv = true;
    private $options = [];
}
```

### 1.2 进程创建机制

```php
// Process 创建和启动流程
public function start(callable $callback = null, array $env = []): void
{
    if ($this->isRunning()) {
        throw new RuntimeException('Process is already running.');
    }

    // 1. 重置进程状态
    $this->resetProcessData();
    
    // 2. 构建命令描述符
    $descriptors = $this->getDescriptors();
    
    // 3. 使用 proc_open 创建进程
    $this->process = @proc_open(
        $this->commandline,
        $descriptors,
        $this->pipes,
        $this->cwd,
        $env ? array_replace($this->env, $env) : $this->env,
        $this->options
    );
    
    // 4. 设置非阻塞模式
    if (\is_resource($this->process)) {
        $this->status = self::STATUS_STARTED;
        $this->pipes = $pipes;
        
        // 设置管道为非阻塞模式
        foreach ($this->pipes as $pipe) {
            stream_set_blocking($pipe, false);
        }
        
        // 记录启动时间
        $this->starttime = microtime(true);
    }
}
```

### 1.3 异步执行实现方式

#### 非阻塞 I/O 实现
```php
// 非阻塞读取输出
public function getIncrementalOutput(): string
{
    $this->readPipesForOutput(__FUNCTION__);
    
    $latest = stream_get_contents(
        $this->pipes[Process::STDOUT], 
        -1, 
        $this->incrementalOutputOffset
    );
    
    $this->incrementalOutputOffset += \strlen($latest);
    
    return $latest;
}

private function readPipesForOutput(string $caller, bool $blocking = false): void
{
    if ($blocking) {
        $this->readPipes(true, $caller);
    } elseif (isset($this->pipes[self::STDOUT]) || isset($this->pipes[self::STDERR])) {
        $r = $this->pipes;
        $w = null;
        $e = null;
        
        // 使用 stream_select 进行非阻塞检查
        if (false === $n = @stream_select($r, $w, $e, 0, 0)) {
            // 选择失败，可能是信号中断
            return;
        }
        
        foreach ($r as $pipe) {
            $this->readPipe($pipe);
        }
    }
}
```

#### 信号处理机制
```php
// 进程信号处理
public function signal(int $signal): self
{
    $this->doSignal($signal, true);
    return $this;
}

private function doSignal(int $signal, bool $throwException): bool
{
    if (null === $pid = $this->getPid()) {
        if ($throwException) {
            throw new LogicException('Can not send signal on a non running process.');
        }
        return false;
    }

    if ('\\' === \DIRECTORY_SEPARATOR) {
        // Windows 平台处理
        exec("taskkill /F /T /PID $pid", $output, $exitCode);
        return 0 === $exitCode;
    }

    // Unix/Linux 平台处理
    return posix_kill($pid, $signal);
}
```

### 1.4 与原生 PHP 进程函数对比

| 功能 | exec() | shell_exec() | proc_open() | Symfony Process |
|------|--------|--------------|-------------|-----------------|
| **执行方式** | 同步阻塞 | 同步阻塞 | 可异步 | 异步友好 |
| **输出获取** | 数组返回 | 字符串返回 | 管道流 | 增量/完整输出 |
| **错误处理** | 退出码 | 无 | 错误流 | 异常+状态码 |
| **进程控制** | 无 | 无 | 基础控制 | 完整控制 |
| **超时处理** | 无 | 无 | 手动实现 | 内置支持 |
| **信号处理** | 无 | 无 | 手动实现 | 内置支持 |
| **实时输出** | 无 | 无 | 复杂实现 | 简单回调 |

#### 代码对比示例

**原生 proc_open（复杂）：**
```php
$descriptorspec = [
    0 => ["pipe", "r"],  // stdin
    1 => ["pipe", "w"],  // stdout
    2 => ["pipe", "w"]   // stderr
];

$process = proc_open($command, $descriptorspec, $pipes);

if (is_resource($process)) {
    // 设置非阻塞
    stream_set_blocking($pipes[1], false);
    stream_set_blocking($pipes[2], false);
    
    // 手动轮询输出
    while (true) {
        $status = proc_get_status($process);
        if (!$status['running']) break;
        
        $output = stream_get_contents($pipes[1]);
        $error = stream_get_contents($pipes[2]);
        
        // 处理输出...
        usleep(100000); // 避免CPU占用过高
    }
    
    // 清理资源
    fclose($pipes[0]);
    fclose($pipes[1]);
    fclose($pipes[2]);
    proc_close($process);
}
```

**Symfony Process（简洁）：**
```php
$process = new Process(['ffmpeg', '-i', $input, $output]);

$process->start(function ($type, $buffer) {
    if (Process::ERR === $type) {
        echo 'ERR > ' . $buffer;
    } else {
        echo 'OUT > ' . $buffer;
    }
});

// 非阻塞等待
while ($process->isRunning()) {
    // 可以执行其他任务
    usleep(100000);
}

echo $process->getOutput();
```

## 2. Symfony Process 在 NativeFFmpegRecorder 中的应用

### 2.1 当前实现分析

```php
// NativeFFmpegRecorder 中的使用
class NativeFFmpegRecorder implements RecorderInterface
{
    public function start(PendingRecorder $pendingRecorder, ?callable $progress = null): RecordHandle
    {
        // 1. 构建 FFmpeg 命令
        $command = $this->buildCommand($pendingRecorder);
        
        // 2. 创建 Symfony Process
        $process = new Process($command);
        $process->setTimeout(null); // 无超时限制
        
        // 3. 启动异步进程
        $process->start($progress);
        
        // 4. 立即返回句柄
        return new RecordHandle(
            recordId: $pendingRecorder->getRecordId(),
            outputPath: $pendingRecorder->getOutputPath(),
            command: $command,
            process: $process
        );
    }
    
    private function buildCommand(PendingRecorder $pendingRecorder): array
    {
        return [
            'ffmpeg',
            '-i', $pendingRecorder->getStream()->getUrl(),
            '-c:v', 'copy',
            '-c:a', 'copy',
            '-f', 'mpegts',
            $pendingRecorder->savePath()
        ];
    }
}
```

### 2.2 异步执行优势

**在 Swoole 协程环境中的表现：**
```php
// SwooleRecordingManager 中的协程友好性
Coroutine::create(function () use ($url) {
    $recorder = new NativeFFmpegRecorder();
    
    // ✅ 这里不会阻塞协程
    $handle = $recorder->start($pendingRecorder, function ($type, $buffer) {
        // 实时处理 FFmpeg 输出
        echo "[$type] $buffer";
    });
    
    // ✅ 可以继续执行其他任务
    while ($handle->isRunning()) {
        // 协程可以在这里让出 CPU
        Coroutine::sleep(0.1);
        
        // 检查进程状态
        if ($handle->hasErrors()) {
            // 处理错误
            break;
        }
    }
    
    // ✅ 获取最终结果
    $exitCode = $handle->getExitCode();
    $output = $handle->getOutput();
});
```

## 3. 核心优势总结

### 3.1 异步执行能力
- **非阻塞启动**：`start()` 方法立即返回
- **实时输出**：通过回调函数获取增量输出
- **状态监控**：可以随时检查进程状态

### 3.2 完善的进程控制
- **信号支持**：可以发送 SIGTERM、SIGKILL 等信号
- **超时管理**：内置超时和空闲超时支持
- **资源清理**：自动管理文件描述符和进程资源

### 3.3 错误处理机制
- **异常封装**：将进程错误转换为 PHP 异常
- **状态码获取**：提供详细的退出状态信息
- **错误输出分离**：独立处理 stdout 和 stderr

### 3.4 跨平台兼容性
- **Windows 支持**：处理 Windows 平台的进程管理差异
- **Unix/Linux 优化**：充分利用 POSIX 信号和进程控制
- **路径处理**：自动处理不同平台的路径分隔符

这种设计使得 NativeFFmpegRecorder 能够在 Swoole 协程环境中完美工作，完全避免了 PhpFFMpegRecorder 的阻塞问题。
