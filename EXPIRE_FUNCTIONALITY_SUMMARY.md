# 直播流过期功能实现总结

## 概述

本次改进为直播流处理功能添加了完整的过期时间支持，包括从URL中解析expire参数、存储过期时间、以及相关的验证和查询功能。

## 实现的功能

### 1. Stream 类增强

#### 新增属性
- `expire` (Carbon|null): 流的过期时间，使用 Carbon 对象存储

#### 新增方法
- `getExpire()`: 获取过期时间
- `setExpire(Carbon|int|null $expire)`: 设置过期时间（支持 Carbon 对象或时间戳）
- `isExpired()`: 检查流是否已过期
- `getTimeToExpire()`: 获取距离过期的剩余时间（秒）
- `normalizeExpire()`: 私有方法，标准化过期时间输入

#### 更新的方法
- 构造函数：添加了 `expire` 参数
- `toArray()`: 包含 expire 和 expire_timestamp 字段
- `getMetadata()`: 包含完整的过期信息
- `equals()`: 比较时考虑过期时间
- `getHash()`: 哈希计算包含过期时间
- `createHls()` 和 `createFlv()`: 静态工厂方法支持 expire 参数
- `fromArray()`: 支持从数组中恢复过期时间（支持时间戳和ISO字符串）

### 2. Douyin Live 类增强

#### 新增方法
- `parseExpireFromUrl(string $url)`: 从URL中解析expire参数

#### 更新的方法
- `getFlvUrl()`: 解析URL中的expire参数并设置到Stream对象
- `getHlsUrl()`: 解析URL中的expire参数并设置到Stream对象
- `findAvailableUrl()`: 智能降级时也会解析expire参数

#### URL解析特性
- 支持多种查询参数格式
- 正确处理expire参数在URL中的不同位置
- 验证时间戳的有效性（拒绝负数和零值）
- 优雅处理无效的expire参数

## 测试覆盖

### Stream 类测试
- **基础功能测试**: 创建、获取、设置过期时间
- **时间计算测试**: 过期检查、剩余时间计算
- **边界情况测试**: 无效时间戳、零值、负值处理
- **静态工厂方法测试**: 带expire参数的流创建
- **数组转换测试**: toArray 和 fromArray 的expire支持
- **元数据测试**: getMetadata 的expire信息
- **相等性和哈希测试**: expire对比较和哈希的影响

### Douyin Live 类测试
- **URL解析测试**: 从真实抖音URL中解析expire参数
- **智能降级测试**: 自动选择清晰度时的expire处理
- **边界情况测试**: 无expire参数、无效expire参数的处理
- **URL格式测试**: 多种查询参数格式的支持

## 使用示例

### 基本使用

```php
use LiveStream\Stream\Stream;
use LiveStream\Enum\Quality;
use Carbon\Carbon;

// 创建带过期时间的流
$expireTime = Carbon::now()->addHours(2);
$stream = Stream::createFlv(
    'https://example.com/stream.flv',
    Quality::HD1,
    false,
    $expireTime
);

// 检查过期状态
if ($stream->isExpired()) {
    echo "流已过期";
} else {
    echo "剩余时间: " . $stream->getTimeToExpire() . " 秒";
}
```

### Douyin Live 使用

```php
use LiveStream\Platforms\Douyin\Live;

// Live 类会自动从URL中解析expire参数
$live = new Live($status, $title, $anchor, $roomId, $streamUrls, $userCount);
$stream = $live->getFlvUrl(Quality::ORIGIN);

if ($stream && $stream->getExpire()) {
    echo "流过期时间: " . $stream->getExpire()->toDateTimeString();
}
```

## 向后兼容性

- 所有现有的API保持不变
- expire参数在所有方法中都是可选的
- 现有的测试全部通过
- 不影响现有功能的性能

## 技术细节

### 时间处理
- 使用 nesbot/carbon 包处理时间
- 支持时间戳和Carbon对象两种输入格式
- 自动验证时间戳的有效性
- 优雅处理时区和格式转换

### URL解析
- 使用 `parse_url()` 和 `parse_str()` 解析查询参数
- 支持expire参数在URL中的任意位置
- 验证解析出的时间戳格式和范围

### 错误处理
- 无效时间戳返回null而不是抛出异常
- URL解析失败时优雅降级
- 所有边界情况都有适当的处理

## 文件变更

### 修改的文件
- `src/Stream/Stream.php`: 添加expire功能
- `src/Platforms/Douyin/Live.php`: 添加URL解析功能
- `tests/Unit/Stream/StreamTest.php`: 更新现有测试，添加expire测试

### 新增的文件
- `tests/Unit/Platforms/Douyin/LiveTest.php`: Douyin Live类的expire功能测试
- `examples/expire_functionality_demo.php`: 功能演示脚本

## 总结

本次实现成功为直播流处理系统添加了完整的过期时间支持，包括：

1. ✅ 完整的expire属性支持（Carbon类型）
2. ✅ 从抖音URL中自动解析expire参数
3. ✅ 全面的单元测试覆盖
4. ✅ 向后兼容性保证
5. ✅ 详细的文档和示例

所有功能都经过了充分的测试验证，可以安全地投入生产使用。
