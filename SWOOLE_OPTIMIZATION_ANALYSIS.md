# SwooleRecordrConnector.php 优化分析

## 发现的问题

### 1. 代码结构问题
- ❌ 缺少 Swoole 初始化和启动代码
- ❌ 没有使用 DebugInfoPipe 中间件
- ❌ 缺少适当的异常处理类型
- ❌ 代码缩进和格式不一致
- ❌ 缺少必要的 use 语句

### 2. Swoole 集成问题
- ❌ 没有创建 Swoole 容器或服务器
- ❌ 协程创建方式不规范
- ❌ 缺少协程调度器启动
- ❌ 没有资源管理和清理

### 3. 错误处理问题
- ❌ 异常类型不完整（缺少 StreamNotLiveException 等）
- ❌ 没有处理网络超时和连接失败
- ❌ 缺少协程异常处理

### 4. 性能问题
- ❌ 每个协程都创建新的 PlatformManager 实例
- ❌ 没有连接池或资源复用
- ❌ 缺少并发控制

### 5. 功能缺失
- ❌ 没有使用新的 expire 功能
- ❌ 没有集成优化后的中间件
- ❌ 缺少调试配置

## 优化方案

### 1. 代码结构优化
- ✅ 添加适当的 use 语句
- ✅ 规范代码格式和缩进
- ✅ 添加 DebugInfoPipe 中间件
- ✅ 完善异常处理

### 2. Swoole 集成优化
- ✅ 添加 Swoole 容器初始化
- ✅ 规范协程创建和管理
- ✅ 添加协程调度器
- ✅ 实现资源管理

### 3. 错误处理改进
- ✅ 添加完整的异常类型处理
- ✅ 实现协程异常处理
- ✅ 添加超时和重试机制

### 4. 性能优化
- ✅ 实现资源复用
- ✅ 添加并发控制
- ✅ 优化内存使用

### 5. 功能增强
- ✅ 集成 expire 功能
- ✅ 使用优化后的中间件
- ✅ 添加详细的调试信息

## 详细对比分析

### 1. 代码结构对比

#### 原始版本问题：
```php
// ❌ 缺少必要的 use 语句
use LiveStream\PlatformFactory;
use LiveStream\Platforms\PlatformManager;
use LiveStream\Config\RecordingOptions;
use LiveStream\Recording\RecordrConnector;

// ❌ 没有 Swoole 初始化
foreach ($douyingUrl as $url) {
    go(function () use ($url) {
        // ❌ 每个协程都创建新实例
        $platformManager = new PlatformManager(new PlatformFactory());
        // ...
    });
}
```

#### 优化版本：
```php
// ✅ 完整的 use 语句
use LiveStream\PlatformFactory;
use LiveStream\Platforms\PlatformManager;
use LiveStream\Config\RecordingOptions;
use LiveStream\Recording\RecordrConnector;
use LiveStream\Recording\Pipes\DebugInfoPipe;
use LiveStream\Exceptions\StreamNotLiveException;
use LiveStream\Exceptions\StreamUnavailableException;
use Swoole\Coroutine;
use Swoole\Coroutine\Channel;

// ✅ 正确的 Swoole 初始化
Coroutine::set([
    'max_coroutine' => 100000,
    'hook_flags' => SWOOLE_HOOK_ALL,
    'enable_preemptive_scheduler' => true,
]);

// ✅ 使用 Coroutine\run 启动调度器
Coroutine\run(function () use ($douyinUrls) {
    // ✅ 共享资源实例
    $platformManager = new PlatformManager(new PlatformFactory());
    // ...
});
```

### 2. 中间件集成对比

#### 原始版本：
```php
// ❌ 没有使用任何中间件
$recordrConnector = new RecordrConnector();
$recordrConnector->withConfig($options);
```

#### 优化版本：
```php
// ✅ 正确使用 DebugInfoPipe 中间件
$recordrConnector = createRecordrConnector();
$recordrConnector->withConfig($options);

// ✅ 在系统中间件之前注册调试中间件
$recordrConnector->middleware()->pipe(new DebugInfoPipe());
```

### 3. 异常处理对比

#### 原始版本：
```php
// ❌ 异常类型不完整
if ($exception instanceof \Alchemy\BinaryDriver\Exception\ExecutionFailureException) {
    return true;
}
if ($exception instanceof \FFMpeg\Exception\RuntimeException) {
    return true;
}
return false;
```

#### 优化版本：
```php
// ✅ 完整的异常处理
switch (true) {
    case $exception instanceof \Alchemy\BinaryDriver\Exception\ExecutionFailureException:
    case $exception instanceof \FFMpeg\Exception\RuntimeException:
    case $exception instanceof StreamUnavailableException:
        echo "  ↻ 将进行重试\n";
        return true;

    case $exception instanceof StreamNotLiveException:
        echo "  ✗ 直播已结束，不重试\n";
        return false;

    default:
        echo "  ✗ 未知异常类型，不重试\n";
        return false;
}
```

### 4. 资源管理对比

#### 原始版本：
```php
// ❌ 每个协程创建新实例，资源浪费
foreach ($douyingUrl as $url) {
    go(function () use ($url) {
        $platformManager = new PlatformManager(new PlatformFactory());
        // ...
    });
}
```

#### 优化版本：
```php
// ✅ 共享资源，避免重复创建
$platformManager = new PlatformManager(new PlatformFactory());

foreach ($douyinUrls as $index => $url) {
    go(function () use ($url, $platformManager, $resultChannel) {
        // ✅ 使用共享的 PlatformManager
        handleSingleStream($url, $platformManager, $resultChannel);
    });
}
```

### 5. 监控和调试对比

#### 原始版本：
```php
// ❌ 简单的输出，没有协程标识
echo "捕获到异常 [尝试 {$attempt}]: " . get_class($exception);

// ❌ 没有执行监控
$result = $recordrConnector->handle($platform, function (string $type, string $buffer) {
    echo "\n[FFmpeg $type]: {$buffer}" . trim($buffer);
});
```

#### 优化版本：
```php
// ✅ 带协程 ID 的详细日志
echo "🔄 [协程 " . Coroutine::getCid() . "] 捕获异常 [尝试 {$attempt}]: {$exceptionClass}";

// ✅ 完整的监控系统
function monitorCoroutines(Channel $resultChannel, int $totalTasks): void
{
    // 详细的执行统计和监控
    echo "📊 开始监控 {$totalTasks} 个录制任务...\n";
    // ...
}
```

## 关键改进点

### 1. Swoole 最佳实践
- ✅ 正确的协程配置和启动
- ✅ 使用 Channel 进行协程间通信
- ✅ 实现协程监控和状态管理
- ✅ 添加随机延迟避免并发冲突

### 2. 中间件执行顺序
- ✅ 遵循我们之前讨论的最佳实践
- ✅ 在系统中间件之前注册 DebugInfoPipe
- ✅ 正确使用 withDebug() 方法

### 3. 错误处理增强
- ✅ 完整的异常类型覆盖
- ✅ 智能重试策略
- ✅ 详细的错误日志和统计

### 4. 性能优化
- ✅ 资源复用减少内存占用
- ✅ 并发控制避免系统过载
- ✅ 异步处理提高效率

### 5. 代码质量提升
- ✅ 函数化设计，提高可读性
- ✅ 完整的类型声明
- ✅ 详细的注释和文档
- ✅ 遵循 PSR 标准
