<?php

declare(strict_types=1);

namespace LiveStream\Exceptions;

/**
 * 流验证异常
 * 
 * 当直播流格式验证失败时抛出
 */
class StreamValidationException extends LiveStreamException
{
    /**
     * 构造函数
     * 
     * @param string $message 异常消息
     * @param int $code 异常代码
     * @param \Throwable|null $previous 前一个异常
     */
    public function __construct(string $message = 'Stream validation failed', int $code = 0, ?\Throwable $previous = null)
    {
        parent::__construct($message, $code, $previous);
    }
}
