<?php

declare(strict_types=1);

namespace LiveStream\Exceptions;

use Throwable;

/**
 * 权限拒绝异常
 * 
 * 当文件或目录操作因权限不足而失败时抛出
 */
class PermissionDeniedException extends LiveStreamException
{
    public function __construct(
        string $path,
        string $operation = 'access',
        ?Throwable $previous = null
    ) {
        $message = "Permission denied for {$operation} operation on path: {$path}";
        parent::__construct($message, 1003, $previous);
    }
}
