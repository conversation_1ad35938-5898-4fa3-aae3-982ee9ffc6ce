<?php

declare(strict_types=1);

namespace LiveStream\Traits;

use LiveStream\Stream\Stream;
use LiveStream\Enum\Quality;
use LiveStream\Contracts\LiveInterface;
use LiveStream\Config\RecordingOptions;
use InvalidArgumentException;

trait HasQuality
{
 
    protected ?Quality $quality = null;

    public function getQuality(): ?Quality
    {
        return $this->quality;
    }

    public function withQuality(?Quality $quality = null): static
    {
        $this->quality = $quality;
        return $this;
    }


}
