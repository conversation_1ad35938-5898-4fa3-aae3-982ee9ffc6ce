<?php

declare(strict_types=1);

namespace LiveStream\Traits;

use LiveStream\Stream\Stream;
use LiveStream\Enum\Quality;
use LiveStream\Contracts\LiveInterface;
use LiveStream\Config\RecordingOptions;
use InvalidArgumentException;


trait HasStreamUrl
{
    protected ?Stream $stream = null;

    public function getStream(): ?Stream
    {
        return $this->stream;
    }

    public function withStream(Stream $stream): static
    {
        $this->stream = $stream;
        return $this;
    }
}
