<?php

declare(strict_types=1);

namespace LiveStream\Traits;

use Closure;

/**
 * Trait for adding retry functionality to connectors
 * 
 * Inspired by Saloon's HasTries trait
 */
trait HasRetry
{
    /**
     * The number of times a request should be retried if a failure occurs.
     * 
     * Set to null or 0 to disable the retry functionality.
     */
    protected ?int $tries = null;

    /**
     * The interval in milliseconds to wait between retries.
     * 
     * For example 1000ms = 1 second.
     * 
     * Set to null or 0 to disable the retry interval.
     */
    protected ?int $retryInterval = null;

    /**
     * Should we use exponential backoff during retries?
     * 
     * When true, the retry interval will be doubled after each attempt.
     */
    protected ?bool $useExponentialBackoff = null;

    /**
     * Callback to determine whether the request should be retried.
     * 
     * The callback receives the exception and attempt number.
     * Return true to retry, false to stop retrying.
     */
    protected ?Closure $shouldRetryCallback = null;

    /**
     * Set the number of retry attempts
     */
    public function withTries(int $tries): static
    {
        $this->tries = $tries;
        return $this;
    }

    /**
     * Get the number of retry attempts
     */
    public function getTries(): int
    {
        return $this->tries ?? 0;
    }

    /**
     * Set the retry interval in milliseconds
     */
    public function withRetryInterval(int $milliseconds): static
    {
        $this->retryInterval = $milliseconds;
        return $this;
    }

    /**
     * Get the retry interval in milliseconds
     */
    public function getRetryInterval(): int
    {
        return $this->retryInterval ?? 0;
    }

    /**
     * Enable or disable exponential backoff
     */
    public function withExponentialBackoff(bool $enabled = true): static
    {
        $this->useExponentialBackoff = $enabled;
        return $this;
    }

    /**
     * Check if exponential backoff is enabled
     */
    public function hasExponentialBackoff(): bool
    {
        return $this->useExponentialBackoff ?? false;
    }

    /**
     * Set a custom callback to determine if retry should happen
     */
    public function withShouldRetry(Closure $callback): static
    {
        $this->shouldRetryCallback = $callback;
        return $this;
    }

    /**
     * Determine whether the request should be retried
     * 
     * @param \Throwable $exception The exception that was thrown
     * @param int $attempt The current attempt number (starting from 1)
     */
    public function shouldRetry(\Throwable $exception, int $attempt): bool
    {
        // If a custom callback is set, use it
        if ($this->shouldRetryCallback !== null) {
            return ($this->shouldRetryCallback)($exception, $attempt);
        }

        // Default behavior: retry on any exception
        return true;
    }

    /**
     * Calculate the delay for the current retry attempt
     * 
     * @param int $attempt The current attempt number (starting from 1)
     */
    protected function calculateRetryDelay(int $attempt): int
    {
        $baseDelay = $this->getRetryInterval();

        if ($baseDelay <= 0) {
            return 0;
        }

        if (!$this->hasExponentialBackoff()) {
            return $baseDelay;
        }

        // Exponential backoff: delay * 2^(attempt-1)
        return $baseDelay * pow(2, $attempt - 1);
    }
}
