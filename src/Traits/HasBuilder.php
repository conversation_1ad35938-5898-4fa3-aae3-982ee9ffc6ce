<?php

declare(strict_types=1);

namespace LiveStream\Traits;

use LiveStream\Recording\Builders\FfmpegCommandBuilder;
use LiveStream\Recording\Contracts\CommandBuilderInterface;

trait HasBuilder
{
 
    protected ?CommandBuilderInterface $builder = null;

    public function builder(): CommandBuilderInterface
    {
        return $this->builder ??= new FfmpegCommandBuilder();
    }

    public function withBuilder(CommandBuilderInterface $builder): static
    {
        $this->builder = $builder;
        return $this;
    }
}
