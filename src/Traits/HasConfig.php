<?php

namespace LiveStream\Traits;

use LiveStream\Config\RecordingOptions;

trait HasConfig
{
    protected ?RecordingOptions $config = null;

    public function config(): RecordingOptions
    {
        return $this->config ??= RecordingOptions::make($this->defaultOptions());
    }

    protected function defaultOptions(): array
    {
        return [];
    }

    public function withConfig(RecordingOptions|array $config): static
    {
        $this->config = is_array($config) ? RecordingOptions::make($config) : $config;
        return $this;
    }
}
