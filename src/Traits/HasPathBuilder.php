<?php

declare(strict_types=1);

namespace LiveStream\Traits;

use Symfony\Component\Filesystem\Filesystem;
use Symfony\Component\Filesystem\Exception\IOExceptionInterface;
use LiveStream\Exceptions\PermissionDeniedException;
use LiveStream\Exceptions\DiskSpaceInsufficientException;

/**
 * 路径构建工具
 * 
 * 负责构建录制文件的输出路径，使用 Symfony Filesystem 组件
 */
trait HasPathBuilder
{
    private ?Filesystem $filesystem = null;

    /**
     * 获取 Filesystem 实例
     */
    private function getFilesystem(): Filesystem
    {
        return $this->filesystem ??= new Filesystem();
    }

    /**
     * 确保输出目录存在
     * 
     * @param string $savePath 文件保存路径
     * @throws PermissionDeniedException 当目录创建失败或无写入权限时
     * @throws DiskSpaceInsufficientException 当磁盘空间不足时
     */
    private function ensureOutputDirectoryExists(string $savePath): void
    {
        $directory = dirname($savePath);
        $filesystem = $this->getFilesystem();

        try {
            // 使用 Symfony Filesystem 创建目录
            if (!$filesystem->exists($directory)) {
                $filesystem->mkdir($directory, 0755);
            }

            // 检查目录是否可写
            if (!is_writable($directory)) {
                throw new PermissionDeniedException($directory, 'write');
            }

            // 检查磁盘空间（可选，预估需要至少 100MB）
            $this->checkDiskSpace($directory, 100 * 1024 * 1024); // 100MB

        } catch (IOExceptionInterface $exception) {
            throw new PermissionDeniedException($directory, 'create', $exception);
        }
    }

    /**
     * 检查磁盘空间是否足够
     * 
     * @param string $path 检查路径
     * @param int $requiredBytes 需要的字节数
     * @throws DiskSpaceInsufficientException 当磁盘空间不足时
     */
    private function checkDiskSpace(string $path, int $requiredBytes): void
    {
        $availableBytes = disk_free_space($path);

        if ($availableBytes === false) {
            // 无法获取磁盘空间信息，跳过检查
            return;
        }

        if ($availableBytes < $requiredBytes) {
            throw new DiskSpaceInsufficientException(
                $path,
                (int) $requiredBytes,
                (int) $availableBytes
            );
        }
    }

    /**
     * 清理文件名中的非法字符
     * 
     * @param string $filename 原始文件名
     * @return string 清理后的文件名
     */
    private function sanitizeFilename(string $filename): string
    {
        // // 移除或替换非法字符
        // $filename = preg_replace('/[<>:"/\\|?*]/', '_', $filename);

        // // 移除控制字符
        // $filename = preg_replace('/[\x00-\x1f\x7f]/', '', $filename);

        // // 移除多个连续的下划线和空格
        // $filename = preg_replace('/[_\s]+/', '_', $filename);

        // // 移除开头和结尾的下划线、点和空格
        // $filename = trim($filename, '_. ');

        // // 如果文件名为空或只包含点，使用默认名称
        // if (empty($filename) || $filename === '.' || $filename === '..') {
        //     $filename = 'unnamed';
        // }

        // // 限制文件名长度（不包括扩展名）
        // if (mb_strlen($filename) > 200) {
        //     $filename = mb_substr($filename, 0, 200);
        // }

        return $filename;
    }

    /**
     * 构建安全的文件路径
     * 
     * @param string $basePath 基础路径
     * @param string ...$segments 路径片段
     * @return string 构建的路径
     */
    private function buildSafePath(string $basePath, string ...$segments): string
    {
        $filesystem = $this->getFilesystem();

        // 清理所有路径片段
        $cleanSegments = array_map([$this, 'sanitizeFilename'], $segments);

        // 构建完整路径
        $fullPath = $basePath;
        foreach ($cleanSegments as $segment) {
            $fullPath = $filesystem->isAbsolutePath($fullPath)
                ? $fullPath . DIRECTORY_SEPARATOR . $segment
                : rtrim($fullPath, DIRECTORY_SEPARATOR) . DIRECTORY_SEPARATOR . $segment;
        }

        return $fullPath;
    }
}
