<?php

declare(strict_types=1);

namespace LiveStream\Platforms\Douyin;

use LiveStream\Contracts\PlatformInterface;
use LiveStream\Contracts\LiveInterface;
use LiveStream\Platforms\Douyin\Http\Connector\DouyinConnector;


class DouyinPlatform implements PlatformInterface
{

    public function __construct(private DouyinConnector $connector, private string $url) {}

    public function getPlatformName(): string
    {
        return 'douyin';
    }

    public function supportsUrl(string $url): bool
    {
        return (bool)preg_match('/(live\\.douyin\\.com|v\\.douyin\\.com|www\\.douyin\\.com)/', $url);
    }

    public function getLive(): LiveInterface
    {
        return $this->connector->resource()->getLive($this->url);
    }

    public function getReferer(): string
    {
        return 'https://live.douyin.com/';
    }
}
