<?php

declare(strict_types=1);

namespace LiveStream\Platforms\Douyin;

use LiveStream\Enum\Quality;
use LiveStream\Contracts\LiveInterface;
use LiveStream\Stream\Stream;
use LiveStream\Enum\StreamType;

class Live implements LiveInterface
{
    public function __construct(
        protected int $status,
        protected string $title,
        protected string $anchorName,
        protected string $roomId,
        protected array $streamUrl,
        protected string $userCountStr
    ) {}

    public function isLive(): bool
    {
        return $this->status === 2;
    }

    public function getFlvUrl(?Quality $quality = null): ?Stream
    {
        // 如果指定了具体的清晰度，直接返回对应的URL
        if ($quality !== null) {
            $url = $this->streamUrl[StreamType::FLV->value][$quality->value] ?? null;
            if ($url) {
                $expire = $this->parseExpireFromUrl($url);
                return Stream::createFlv($url, $quality, false, $expire);
            }
            return null;
        }

        // 如果quality为null，使用智能降级逻辑
        return $this->findAvailableUrl(StreamType::FLV);
    }

    public function getHlsUrl(?Quality $quality = null): ?Stream
    {
        // 如果指定了具体的清晰度，直接返回对应的URL
        if ($quality !== null) {
            $url = $this->streamUrl[StreamType::HLS->value][$quality->value] ?? null;
            if ($url) {
                $expire = $this->parseExpireFromUrl($url);
                return Stream::createHls($url, $quality, false, $expire);
            }
            return null;
        }

        // 如果quality为null，使用智能降级逻辑
        return $this->findAvailableUrl(StreamType::HLS);
    }

    public function getStreamUrl(): array
    {
        return $this->streamUrl;
    }

    public function getAnchorName(): string
    {
        return $this->anchorName;
    }

    public function getTitle(): string
    {
        return $this->title;
    }

    public function getRoomId(): string
    {
        return $this->roomId;
    }

    public function getUserCountStr(): string
    {
        return $this->userCountStr;
    }

    /**
     * 智能降级：按优先级顺序查找可用的流URL
     *
     * @param StreamType $streamType URL类型：'flv' 或 'hls'
     * @return Stream|null 找到的第一个可用Stream对象，如果都不可用则返回null
     */
    public function findAvailableUrl(StreamType $streamType): ?Stream
    {
        // 检查streamUrl结构是否有效
        if (!isset($this->streamUrl[$streamType->value]) || !is_array($this->streamUrl[$streamType->value])) {
            return null;
        }

        $urlMap = $this->streamUrl[$streamType->value];

        // 按优先级顺序尝试每个清晰度
        foreach (Quality::getPriorityOrder() as $quality) {
            $url = $urlMap[$quality->value] ?? null;

            // 检查URL是否存在且不为空字符串
            if ($url !== null && $url !== '') {
                $expire = $this->parseExpireFromUrl($url);

                if ($streamType->value === StreamType::FLV->value) {
                    return Stream::createFlv($url, $quality, false, $expire);
                } else if ($streamType->value === StreamType::HLS->value) {
                    return Stream::createHls($url, $quality, false, $expire);
                }
            }
        }

        return null;
    }

    /**
     * 从URL中解析expire参数
     *
     * @param string $url 流URL
     * @return int|null 过期时间戳，如果未找到则返回null
     */
    private function parseExpireFromUrl(string $url): ?int
    {
        // 解析URL的查询参数
        $parsedUrl = parse_url($url);
        if (!isset($parsedUrl['query'])) {
            return null;
        }

        // 解析查询字符串
        parse_str($parsedUrl['query'], $queryParams);

        // 检查是否存在expire参数
        if (!isset($queryParams['expire'])) {
            return null;
        }

        $expire = $queryParams['expire'];

        // 验证expire参数是否为有效的时间戳
        if (!is_numeric($expire)) {
            return null;
        }

        $timestamp = (int) $expire;

        // 验证时间戳是否合理（不能是负数，且不能太小）
        if ($timestamp <= 0) {
            return null;
        }

        return $timestamp;
    }
}
