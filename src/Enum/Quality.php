<?php

declare(strict_types=1);

namespace LiveStream\Enum;


enum Quality: string
{
    case ORIGIN = 'ORIGIN';
    case FULL_HD1 = 'FULL_HD1';
    case HD1 = 'HD1';
    case SD1 = 'SD1';
    case SD2 = 'SD2';

    /**
     * 获取画质的中文描述
     */
    public function getDisplayName(): string
    {
        return match ($this) {
            self::ORIGIN => '原画',
            self::FULL_HD1 => '超清',
            self::HD1 => '高清',
            self::SD1 => '标清',
            self::SD2 => '流畅',
        };
    }

    /**
     * 从中文描述创建枚举
     */
    public static function fromDisplayName(string $name): ?self
    {
        return match ($name) {
            '原画' => self::ORIGIN,
            '超清' => self::FULL_HD1,
            '高清' => self::HD1,
            '标清' => self::SD1,
            '流畅' => self::SD2,
            default => null,
        };
    }

    /**
     * 获取所有支持的画质选项
     *
     * @return array<string, string>
     */
    public static function getOptions(): array
    {
        return [
            self::ORIGIN->value => self::ORIGIN->getDisplayName(),
            self::FULL_HD1->value => self::FULL_HD1->getDisplayName(),
            self::HD1->value => self::HD1->getDisplayName(),
            self::SD1->value => self::SD1->getDisplayName(),
            self::SD2->value => self::SD2->getDisplayName(),
        ];
    }

    /**
     * 获取按优先级排序的画质列表（从高到低）
     * 用于智能降级功能
     *
     * @return Quality[]
     */
    public static function getPriorityOrder(): array
    {
        return [
            self::ORIGIN,    // 原画 - 最高清晰度
            self::FULL_HD1,  // 超清 - 1080p
            self::HD1,       // 高清 - 720p
            self::SD1,       // 标清 - 480p
            self::SD2,       // 流畅 - 360p或更低
        ];
    }

    /**
     * 获取画质的优先级数值（数值越小优先级越高）
     *
     * @return int
     */
    public function getPriority(): int
    {
        return match ($this) {
            self::ORIGIN => 1,
            self::FULL_HD1 => 2,
            self::HD1 => 3,
            self::SD1 => 4,
            self::SD2 => 5,
        };
    }
}
