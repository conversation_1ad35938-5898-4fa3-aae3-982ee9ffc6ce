<?php

declare(strict_types=1);

namespace LiveStream\Stream;

use Carbon\Carbon;
use LiveStream\Enum\Quality;

/**
 * 流数据类
 * 
 * 封装流URL和相关元数据，提供验证功能
 */
class Stream
{
    /**
     * 验证状态
     */
    private bool $validated = false;

    /**
     * 流过期时间
     */
    private ?Carbon $expire = null;

    /**
     * 构造函数
     *
     * @param string $url 流URL
     * @param string $type 流类型：'hls' 或 'flv'
     * @param Quality|null $quality 清晰度
     * @param bool $validated 是否已验证
     * @param Carbon|int|null $expire 过期时间（Carbon对象或时间戳）
     */
    public function __construct(
        private readonly string $url,
        private readonly string $type,
        private readonly ?Quality $quality = null,
        bool $validated = false,
        Carbon|int|null $expire = null
    ) {
        $this->validated = $validated;
        $this->expire = $this->normalizeExpire($expire);
    }

    /**
     * 获取流URL
     * 
     * @return string 流URL
     */
    public function getUrl(): string
    {
        return $this->url;
    }

    /**
     * 获取流类型
     * 
     * @return string 流类型
     */
    public function getType(): string
    {
        return $this->type;
    }

    /**
     * 获取清晰度
     * 
     * @return Quality|null 清晰度
     */
    public function getQuality(): ?Quality
    {
        return $this->quality;
    }

    /**
     * 检查是否已验证
     *
     * @return bool 是否已验证
     */
    public function isValidated(): bool
    {
        return $this->validated;
    }

    /**
     * 获取过期时间
     *
     * @return Carbon|null 过期时间
     */
    public function getExpire(): ?Carbon
    {
        return $this->expire;
    }

    /**
     * 设置过期时间
     *
     * @param Carbon|int|null $expire 过期时间（Carbon对象或时间戳）
     * @return self
     */
    public function setExpire(Carbon|int|null $expire): self
    {
        $this->expire = $this->normalizeExpire($expire);
        return $this;
    }

    /**
     * 检查流是否已过期
     *
     * @return bool 是否已过期
     */
    public function isExpired(): bool
    {
        if ($this->expire === null) {
            return false;
        }

        return $this->expire->isPast();
    }

    /**
     * 获取距离过期的剩余时间（秒）
     *
     * @return int|null 剩余秒数，null表示无过期时间
     */
    public function getTimeToExpire(): ?int
    {
        if ($this->expire === null) {
            return null;
        }

        $now = Carbon::now();
        if ($this->expire->isPast()) {
            return 0;
        }

        return (int) $now->diffInSeconds($this->expire);
    }

    /**
     * 标准化过期时间
     *
     * @param Carbon|int|null $expire 过期时间
     * @return Carbon|null 标准化后的Carbon对象
     */
    private function normalizeExpire(Carbon|int|null $expire): ?Carbon
    {
        if ($expire === null) {
            return null;
        }

        if ($expire instanceof Carbon) {
            return $expire;
        }

        // 处理时间戳
        if (is_int($expire)) {
            // 验证时间戳是否合理（不能是负数，且不能太小）
            if ($expire <= 0) {
                return null;
            }

            try {
                return Carbon::createFromTimestamp($expire);
            } catch (\Throwable) {
                return null;
            }
        }

        return null;
    }

    /**
     * 验证URL格式
     * 
     * @return bool URL是否有效
     */
    public function validateUrl(): bool
    {
        return filter_var($this->url, FILTER_VALIDATE_URL) !== false;
    }

    /**
     * 验证流格式
     *
     * @return bool 格式是否匹配
     */
    public function validateFormat(): bool
    {
        switch ($this->type) {
            case 'hls':
                return strpos($this->url, '.m3u8') !== false || strpos($this->url, 'format=m3u8') !== false;
            case 'flv':
                return strpos($this->url, '.flv') !== false || strpos($this->url, 'format=flv') !== false;
            default:
                return false; // 未知类型
        }
    }

    /**
     * 验证HTTP连接
     * 
     * @return bool 连接是否成功
     */
    public function validateConnection(): bool
    {
        try {
            $context = stream_context_create([
                'http' => [
                    'method' => 'HEAD',
                    'timeout' => 10,
                    'user_agent' => 'LiveStream/1.0',
                    'follow_location' => true,
                    'max_redirects' => 3,
                ],
            ]);

            $headers = @get_headers($this->url, false, $context);

            if ($headers === false) {
                return false;
            }

            // 检查HTTP状态码
            $statusLine = $headers[0] ?? '';
            return strpos($statusLine, '200') !== false ||
                strpos($statusLine, '302') !== false ||
                strpos($statusLine, '301') !== false;
        } catch (\Throwable $e) {
            return false;
        }
    }

    /**
     * 完整验证
     * 
     * 验证URL格式、流格式和HTTP连接
     * 
     * @return bool 是否通过所有验证
     */
    public function validate(): bool
    {
        $isValid = $this->validateUrl() &&
            $this->validateFormat() &&
            $this->validateConnection();

        $this->validated = $isValid;
        return $isValid;
    }

    /**
     * 检查是否为HLS流
     * 
     * @return bool 是否为HLS流
     */
    public function isHls(): bool
    {
        return $this->type === 'hls';
    }

    /**
     * 检查是否为FLV流
     * 
     * @return bool 是否为FLV流
     */
    public function isFlv(): bool
    {
        return $this->type === 'flv';
    }

    /**
     * 获取清晰度显示名称
     * 
     * @return string|null 清晰度显示名称
     */
    public function getQualityDisplayName(): ?string
    {
        return $this->quality?->getDisplayName();
    }

    /**
     * 转换为数组
     *
     * @return array 流信息数组
     */
    public function toArray(): array
    {
        return [
            'url' => $this->url,
            'type' => $this->type,
            'quality' => $this->quality,
            'validated' => $this->validated,
            'expire' => $this->expire?->toISOString(),
            'expire_timestamp' => $this->expire?->timestamp,
        ];
    }

    /**
     * 转换为字符串（返回URL）
     * 
     * @return string 流URL
     */
    public function __toString(): string
    {
        return $this->url;
    }

    /**
     * 创建HLS流对象
     *
     * @param string $url 流URL
     * @param Quality|null $quality 清晰度
     * @param bool $validated 是否已验证
     * @param Carbon|int|null $expire 过期时间
     * @return self HLS流对象
     */
    public static function createHls(string $url, ?Quality $quality = null, bool $validated = false, Carbon|int|null $expire = null): self
    {
        return new self($url, 'hls', $quality, $validated, $expire);
    }

    /**
     * 创建FLV流对象
     *
     * @param string $url 流URL
     * @param Quality|null $quality 清晰度
     * @param bool $validated 是否已验证
     * @param Carbon|int|null $expire 过期时间
     * @return self FLV流对象
     */
    public static function createFlv(string $url, ?Quality $quality = null, bool $validated = false, Carbon|int|null $expire = null): self
    {
        return new self($url, 'flv', $quality, $validated, $expire);
    }

    /**
     * 从数组创建流对象
     *
     * @param array $data 流数据数组
     * @return self 流对象
     */
    public static function fromArray(array $data): self
    {
        $expire = null;
        if (isset($data['expire_timestamp'])) {
            $expire = $data['expire_timestamp'];
        } elseif (isset($data['expire'])) {
            // 尝试解析 ISO 字符串格式的日期
            if (is_string($data['expire'])) {
                try {
                    $expire = Carbon::parse($data['expire']);
                } catch (\Throwable) {
                    $expire = null;
                }
            } else {
                $expire = $data['expire'];
            }
        }

        return new self(
            $data['url'],
            $data['type'],
            $data['quality'] ?? null,
            $data['validated'] ?? false,
            $expire
        );
    }

    /**
     * 比较两个流对象是否相等
     *
     * @param Stream $other 另一个流对象
     * @return bool 是否相等
     */
    public function equals(Stream $other): bool
    {
        return $this->url === $other->url &&
            $this->type === $other->type &&
            $this->quality === $other->quality &&
            $this->expire?->timestamp === $other->expire?->timestamp;
    }

    /**
     * 获取流的哈希值
     *
     * @return string 哈希值
     */
    public function getHash(): string
    {
        return md5($this->url . $this->type . ($this->quality?->value ?? '') . ($this->expire?->timestamp ?? ''));
    }

    /**
     * 检查流是否可用
     * 
     * 快速检查，只验证URL格式和流格式，不进行网络连接
     * 
     * @return bool 是否可用
     */
    public function isAvailable(): bool
    {
        return $this->validateUrl() && $this->validateFormat();
    }

    /**
     * 获取流的元数据信息
     *
     * @return array 元数据信息
     */
    public function getMetadata(): array
    {
        return [
            'url' => $this->url,
            'type' => $this->type,
            'quality' => [
                'value' => $this->quality?->value,
                'display_name' => $this->quality?->getDisplayName(),
                'priority' => $this->quality?->getPriority(),
            ],
            'validated' => $this->validated,
            'is_hls' => $this->isHls(),
            'is_flv' => $this->isFlv(),
            'hash' => $this->getHash(),
            'expire' => [
                'timestamp' => $this->expire?->timestamp,
                'iso_string' => $this->expire?->toISOString(),
                'human_readable' => $this->expire?->toDateTimeString(),
                'is_expired' => $this->isExpired(),
                'time_to_expire' => $this->getTimeToExpire(),
            ],
        ];
    }
}
