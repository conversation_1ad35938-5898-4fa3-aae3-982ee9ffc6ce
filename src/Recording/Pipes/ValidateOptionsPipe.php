<?php

namespace LiveStream\Recording\Pipes;

use LiveStream\Exceptions\StreamNotLiveException;
use LiveStream\Recording\PendingRecorder;

class ValidateOptionsPipe
{
    public function handle(PendingRecorder $pendingRecorder, \Closure $next): mixed
    {
        if (!$pendingRecorder->getLive()->isLive()) {
            throw new StreamNotLiveException(
                message:"{$pendingRecorder->getLive()->getAnchorName()} Stream is not live",
            );
        }

        return $next($pendingRecorder);
    }
}
