<?php

declare(strict_types=1);

namespace LiveStream\Recording\Drivers;

use FFMpeg\FFMpeg;
use FFMpeg\Format\Video\X264;
use FFMpeg\Format\FormatInterface;
use FFMpeg\Format\VideoInterface;
use LiveStream\Recording\Contracts\RecorderInterface;
use LiveStream\Recording\PendingRecorder;
use LiveStream\Recording\RecordHandle;
use Symfony\Component\Process\Process;

/**
 * 优化的 PhpFFMpegRecorder 实现
 *
 * 解决的问题：
 * 1. 双通道编码导致的性能浪费
 * 2. 同步阻塞问题
 * 3. 不必要的编码器配置
 */
final class PhpFFMpegRecorder implements RecorderInterface
{
    /**
     * 异步启动录制（推荐使用）
     *
     * @param PendingRecorder $pendingRecorder 录制配置
     * @param callable|null $progress 进度回调
     * @return RecordHandle 可控制的录制句柄
     */
    public function start(PendingRecorder $pendingRecorder, ?callable $progress = null): RecordHandle
    {
        try {
            // 1. 提取优化的 FFmpeg 命令
            $command = $this->extractOptimizedCommand($pendingRecorder);

            // 2. 创建异步进程
            $process = new Process($command);
            $process->setTimeout(null); // 直播录制无超时限制

            // 3. 启动异步进程
            $process->start();

            // 4. 设置进度回调（非阻塞方式）
            if ($progress !== null) {
                $this->setupAsyncProgressCallback($process, $progress);
            }

            // 5. 立即返回可控制的句柄
            return new RecordHandle(
                recordId: $pendingRecorder->getRecordId(),
                outputPath: $pendingRecorder->getOutputPath(),
                command: $command,
                process: $process
            );
        } catch (\Throwable $e) {
            throw $e;
        }
    }

    /**
     * 异步启动录制（兼容原有接口）
     *
     * @param PendingRecorder $pendingRecorder 录制配置
     * @param callable|null $progress 进度回调
     * @return RecordHandle 可控制的录制句柄
     */
    public function startSync(PendingRecorder $pendingRecorder, ?callable $progress = null): RecordHandle
    {
        try {
            // 1. 提取优化的 FFmpeg 命令
            $command = $this->extractOptimizedCommand($pendingRecorder);

            // 2. 创建异步进程
            $process = new Process($command);
            $process->setTimeout(null); // 直播录制无超时限制

            // 3. 启动异步进程
            $process->start();

            // 4. 设置进度回调（非阻塞方式）
            if ($progress !== null) {
                $this->setupAsyncProgressCallback($process, $progress);
            }

            // 5. 立即返回可控制的句柄
            return new RecordHandle(
                recordId: $pendingRecorder->getRecordId(),
                outputPath: $pendingRecorder->getOutputPath(),
                command: $command,
                process: $process
            );
        } catch (\Throwable $e) {
            throw $e;
        }
    }

    /**
     * 提取优化的 FFmpeg 命令
     *
     * 核心优化：
     * 1. 移除不必要的编码器配置
     * 2. 强制单通道编码
     * 3. 使用流复制模式
     *
     * @param PendingRecorder $pendingRecorder 录制配置
     * @return array FFmpeg 命令数组
     */
    private function extractOptimizedCommand(PendingRecorder $pendingRecorder): array
    {
        try {
            $streamUrl = $pendingRecorder->getStream();
            $config = $pendingRecorder->recordrConnector()->config()->all();

            // 🔴 关键优化：直接构建流复制命令，避免 php-ffmpeg 的复杂性
            return $this->buildStreamCopyCommand(
                $streamUrl->getUrl(),
                $pendingRecorder->savePath(),
                $config
            );
        } catch (\Throwable $e) {
            throw new \RuntimeException("命令提取失败: " . $e->getMessage(), 0, $e);
        }
    }

    /**
     * 构建优化的流复制命令
     *
     * 🔴 关键优化：完全避免 php-ffmpeg 的双通道编码问题
     */
    private function buildStreamCopyCommand(string $inputUrl, string $outputPath, array $config): array
    {
        $command = ['ffmpeg', '-y'];

        // 添加网络优化参数
        $command = array_merge($command, [
            '-rw_timeout',
            '15000000',
            '-analyzeduration',
            '20000000',
            '-probesize',
            '10000000',
            '-protocol_whitelist',
            'rtmp,crypto,file,http,https,tcp,tls,udp,rtp,httpproxy',
            '-thread_queue_size',
            '1024',
            '-fflags',
            '+discardcorrupt'
        ]);

        // 添加请求头（如果配置中有）
        if (isset($config['custom_headers']) && !empty($config['custom_headers'])) {
            $headerLines = [];
            foreach ($config['custom_headers'] as $k => $v) {
                $headerLines[] = sprintf('%s: %s', $k, $v);
            }
            $command[] = '-headers';
            $command[] = implode('\r\n', $headerLines);
        }

        // 输入源
        $command[] = '-i';
        $command[] = $inputUrl;

        // 添加输入后的网络参数
        $command = array_merge($command, [
            '-bufsize',
            '8000k',
            '-sn',
            '-dn',  // 禁用字幕和数据流
            '-reconnect_delay_max',
            '60',
            '-reconnect_streamed',
            '-reconnect_at_eof',
            '-max_muxing_queue_size',
            '1024',
            '-correct_ts_overflow',
            '1',
            '-avoid_negative_ts',
            '1'
        ]);

        // 🔴 关键：流复制参数（不重新编码）
        $command = array_merge($command, [
            '-c',
            'copy',           // 复制所有流
            '-f',
            'mpegts',         // 使用 TS 格式
            '-map',
            '0'             // 映射所有输入流
        ]);

        // 输出文件
        $command[] = $outputPath;

        return $command;
    }

    /**
     * 设置异步进度回调
     */
    private function setupAsyncProgressCallback(Process $process, callable $progress): void
    {
        // 使用非阻塞方式监控进度
        // 注意：这里不能使用 wait() 或其他阻塞方法

        // 简单的非阻塞进度监控
        if (function_exists('pcntl_fork')) {
            $pid = pcntl_fork();
            if ($pid === 0) {
                // 子进程中监控进度
                while ($process->isRunning()) {
                    $output = $process->getIncrementalOutput();
                    $errorOutput = $process->getIncrementalErrorOutput();

                    if (!empty($output)) {
                        $progress('stdout', $output);
                    }

                    if (!empty($errorOutput)) {
                        $progress('stderr', $errorOutput);
                    }

                    usleep(100000); // 100ms
                }
                exit(0);
            }
        }
        // 如果不支持 fork，进度回调将在进程结束后才能获取到输出
        // 这是 Symfony Process 在某些环境下的限制
    }
}
