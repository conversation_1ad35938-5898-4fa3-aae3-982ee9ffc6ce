<?php

declare(strict_types=1);

namespace LiveStream\Recording\Drivers;

use LiveStream\Recording\Contracts\RecorderInterface;
use LiveStream\Recording\PendingRecorder;
use Symfony\Component\Process\Process;


final class PhpFFMpegRecorder implements RecorderInterface
{
    /**
     * 同步启动录制（阻塞执行）
     *
     * @param PendingRecorder $pendingRecorder 录制配置
     * @param callable|null $progress 进度回调
     * @return PendingRecorder 录制句柄
     */
    public function start(PendingRecorder $pendingRecorder, ?callable $progress = null): PendingRecorder
    {
        try {

            $command = $pendingRecorder->buildCommand();

            $process = new Process($command);
            $process->setTimeout(null); // 无超时限制，录制可能很长

            // 设置进度回调（非阻塞）
            if ($progress !== null) {
                $process->run(function ($type, $buffer) use ($progress) {
                    $progress($type, $buffer);
                });
            } else {
                $process->run();
            }

            // 检查进程是否成功启动
            if (!$process->isRunning() && !$process->isSuccessful()) {
                throw new \RuntimeException(
                    sprintf(
                        "录制进程启动失败: %s\n命令: %s\n错误输出: %s",
                        $process->getExitCodeText(),
                        implode(' ', $command),
                        $process->getErrorOutput()
                    )
                );
            }

            $process->wait();

            // 返回已完成的句柄
            return $pendingRecorder;
        } catch (\Throwable $e) {
            throw $e;
        }
    }

    /**
     * 异步启动录制（非阻塞执行）
     *
     * @param PendingRecorder $pendingRecorder 录制配置
     * @param callable|null $progress 进度回调
     * @return PendingRecorder 可控制的录制句柄
     */
    public function startSync(PendingRecorder $pendingRecorder, ?callable $progress = null): PendingRecorder
    {
        try {
            // 提取优化的 FFmpeg 命令
            $command = $pendingRecorder->buildCommand();

            // 创建异步进程
            $process = new Process($command);
            $process->setTimeout(null); // 无超时限制，录制可能很长

            // 设置进度回调（非阻塞）
            if ($progress !== null) {
                $process->start(function ($type, $buffer) use ($progress) {
                    $progress($type, $buffer);
                });
            } else {
                $process->start();
            }

            // 检查进程是否成功启动
            if (!$process->isRunning() && !$process->isSuccessful()) {
                throw new \RuntimeException(
                    sprintf(
                        "录制进程启动失败: %s\n命令: %s\n错误输出: %s",
                        $process->getExitCodeText(),
                        implode(' ', $command),
                        $process->getErrorOutput()
                    )
                );
            }

            //这里调试，暂时阻塞
            $process->wait();

            // 立即返回可控制的句柄
            return $pendingRecorder;
        } catch (\Throwable $e) {
            throw $e;
        }
    }
}
