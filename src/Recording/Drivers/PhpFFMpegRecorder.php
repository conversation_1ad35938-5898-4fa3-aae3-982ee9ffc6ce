<?php

declare(strict_types=1);

namespace LiveStream\Recording\Drivers;

use FFMpeg\FFMpeg;
use FFMpeg\Format\Video\X264;
use LiveStream\Recording\Contracts\RecorderInterface;
use LiveStream\Recording\PendingRecorder;
use LiveStream\Recording\RecordHandle;
use Symfony\Component\Process\Process;

/**
 * 优化的 PhpFFMpegRecorder 实现
 *
 * 解决双通道编码问题，提供同步和异步两种执行方式
 */
final class PhpFFMpegRecorder implements RecorderInterface
{
    /**
     * 同步启动录制（阻塞执行）
     *
     * @param PendingRecorder $pendingRecorder 录制配置
     * @param callable|null $progress 进度回调
     * @return RecordHandle 录制句柄
     */
    public function start(PendingRecorder $pendingRecorder, ?callable $progress = null):void
    {
        try {

            $ffmpeg = FFMpeg::create($pendingRecorder->recordrConnector()->config()->all());

            // 从PendingRecorder获取预验证的流URL
            $streamUrl = $pendingRecorder->getStream();

            // 1. 使用 FFMpeg 打开媒体流
            $media = $ffmpeg->open(
                $streamUrl->getUrl()
            );

            // 创建优化的格式配置
            $format = $this->createOptimizedFormat();

            // 设置进度回调
            if ($progress !== null) {
                $format->on('progress', function ($media, $format, $percentage) use ($progress) {
                    $progress('stdout', "progress: {$percentage}%");
                });
            }

            // 同步执行录制（阻塞）
            $media->save($format, $pendingRecorder->savePath());

            // 返回已完成的句柄

        } catch (\Throwable $e) {
            throw $e;
        }
    }

    /**
     * 异步启动录制（非阻塞执行）
     *
     * @param PendingRecorder $pendingRecorder 录制配置
     * @param callable|null $progress 进度回调
     * @return PendingRecorder 可控制的录制句柄
     */
    public function startSync(PendingRecorder $pendingRecorder, ?callable $progress = null): PendingRecorder
    {
        try {
            // 提取优化的 FFmpeg 命令
            $command = $this->extractOptimizedCommand($pendingRecorder);

            // 创建异步进程
            $process = new Process($command);
            $process->setTimeout(null);

            // 启动异步进程
            $process->start();

            // 设置进度回调（非阻塞）
            if ($progress !== null) {
                $this->setupAsyncProgressCallback($process, $progress);
            }



            // // 立即返回可控制的句柄
            // return new RecordHandle(
            //     recordId: $pendingRecorder->getRecordId(),
            //     outputPath: $pendingRecorder->getOutputPath(),
            //     command: $command,
            //     process: $process
            // );

            return $pendingRecorder;

        } catch (\Throwable $e) {
            throw $e;
        }
    }

    /**
     * 提取优化的 FFmpeg 命令
     *
     * 使用 php-ffmpeg 的原生命令生成，但应用单通道优化
     *
     * @param PendingRecorder $pendingRecorder 录制配置
     * @return array FFmpeg 命令数组
     */
    private function extractOptimizedCommand(PendingRecorder $pendingRecorder): array
    {
        try {
            $streamUrl = $pendingRecorder->getStream();
            $ffmpeg = FFMpeg::create($pendingRecorder->recordrConnector()->config()->all());
            $media = $ffmpeg->open($streamUrl->getUrl());

            // 创建优化的格式配置
            $format = $this->createOptimizedFormat();

            // 使用 php-ffmpeg 的原生命令生成
            $commands = $media->getFinalCommand($format, $pendingRecorder->savePath());

            if (empty($commands)) {
                throw new \RuntimeException("无法生成 FFmpeg 命令");
            }

            // 返回第一个命令（单通道优化后只有一个命令）
            return $this->parseCommandString($commands[0]);
        } catch (\Throwable $e) {
            throw new \RuntimeException("命令提取失败: " . $e->getMessage(), 0, $e);
        }
    }

    /**
     * 创建优化的格式配置
     *
     * 关键优化：强制单通道编码，使用流复制
     */
    private function createOptimizedFormat(): X264
    {
        $format = new X264('aac', 'libx264');

        // 🔴 关键优化：强制单通道编码
        $format->setPasses(1);
        $format->setKiloBitrate(0);  // 触发单通道模式
        $format->setAudioKiloBitrate(128);

        // 🔴 关键优化：流复制参数
        $additionalParams = [
            '-c:v',
            'copy',
            '-c:a',
            'copy',
            '-map',
            '0',
            '-f',
            'mpegts'
        ];
        $format->setAdditionalParameters($additionalParams);

        return $format;
    }

    /**
     * 解析命令字符串为数组
     */
    private function parseCommandString(string $commandString): array
    {
        // 简单的命令解析
        $parts = explode(' ', $commandString);
        return array_filter($parts, fn($part) => !empty(trim($part)));
    }

   
}
