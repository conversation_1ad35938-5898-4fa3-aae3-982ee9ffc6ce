<?php

declare(strict_types=1);

namespace LiveStream\Recording\Drivers;

use FFMpeg\FFMpeg;
use FFMpeg\Format\Video\X264;
use LiveStream\Recording\Contracts\RecorderInterface;
use LiveStream\Recording\PendingRecorder;
use LiveStream\Recording\ValueObjects\RecordHandle;
use Symfony\Component\Process\Process;

/**
 * 优化的 PhpFFMpegRecorder 实现
 *
 * 解决双通道编码问题，提供同步和异步两种执行方式
 */
final class PhpFFMpegRecorder implements RecorderInterface
{
    /**
     * 同步启动录制（阻塞执行）
     *
     * @param PendingRecorder $pendingRecorder 录制配置
     * @param callable|null $progress 进度回调
     * @return PendingRecorder 录制句柄
     */
    public function start(PendingRecorder $pendingRecorder, ?callable $progress = null): PendingRecorder
    {
        try {

            $ffmpeg = FFMpeg::create($pendingRecorder->recordrConnector()->config()->all());

            // 从PendingRecorder获取预验证的流URL
            $streamUrl = $pendingRecorder->getStream();

            // 1. 使用 FFMpeg 打开媒体流
            $media = $ffmpeg->open(
                $streamUrl->getUrl()
            );

            // 创建优化的格式配置
            $format = $this->createOptimizedFormat();

            // 设置进度回调
            if ($progress !== null) {
                $format->on('progress', function ($media, $format, $percentage) use ($progress) {
                    $progress('stdout', "progress: {$percentage}%");
                });
            }

            // 同步执行录制（阻塞）
            $media->save($format, $pendingRecorder->savePath());

            // 返回已完成的句柄
            return $pendingRecorder;
        } catch (\Throwable $e) {
            throw $e;
        }
    }

    /**
     * 异步启动录制（非阻塞执行）
     *
     * @param PendingRecorder $pendingRecorder 录制配置
     * @param callable|null $progress 进度回调
     * @return PendingRecorder 可控制的录制句柄
     */
    public function startSync(PendingRecorder $pendingRecorder, ?callable $progress = null): PendingRecorder
    {
        try {
            // 提取优化的 FFmpeg 命令
            $command = $this->extractOptimizedCommand($pendingRecorder);

            // 创建异步进程
            $process = new Process($command);
            $process->setTimeout(null); // 无超时限制，录制可能很长

            // 设置进度回调（非阻塞）
            if ($progress !== null) {
                $process->start(function ($type, $buffer) use ($progress) {
                    $progress($type, $buffer);
                });
            } else {
                $process->start();
            }

            // 检查进程是否成功启动
            if (!$process->isRunning() && !$process->isSuccessful()) {
                throw new \RuntimeException(
                    sprintf(
                        "录制进程启动失败: %s\n命令: %s\n错误输出: %s",
                        $process->getExitCodeText(),
                        implode(' ', $command),
                        $process->getErrorOutput()
                    )
                );
            }

            $process->wait();

            // 立即返回可控制的句柄
            return $pendingRecorder;
        } catch (\Throwable $e) {
            throw $e;
        }
    }

    /**
     * 提取优化的 FFmpeg 命令
     *
     * 使用 php-ffmpeg 的原生命令生成，但应用单通道优化
     *
     * @param PendingRecorder $pendingRecorder 录制配置
     * @return array FFmpeg 命令数组
     */
    private function extractOptimizedCommand(PendingRecorder $pendingRecorder): array
    {
        try {
            $streamUrl = $pendingRecorder->getStream();
            $ffmpeg = FFMpeg::create($pendingRecorder->recordrConnector()->config()->all());
            $media = $ffmpeg->open($streamUrl->getUrl());

            // 创建优化的格式配置
            $format = $this->createOptimizedFormat();

            // 使用 php-ffmpeg 的原生命令生成
            $commands = $media->getFinalCommand($format, $pendingRecorder->savePath());

            if (empty($commands)) {
                throw new \RuntimeException("无法生成 FFmpeg 命令");
            }

            // 🔴 关键修复：php-ffmpeg 返回的命令字符串不包含 "ffmpeg" 前缀
            // 我们需要手动添加并正确解析
            $commandString = $commands[0];

            // 解析命令字符串并确保以 ffmpeg 开头
            return $this->parseCommandString($commandString);
        } catch (\Throwable $e) {
            throw new \RuntimeException("命令提取失败: " . $e->getMessage(), 0, $e);
        }
    }

    /**
     * 创建优化的格式配置
     *
     * 修复流映射错误，使用更安全的参数配置
     */
    private function createOptimizedFormat(): X264
    {
        $format = new X264('aac', 'libx264');

        // 🔴 关键修复：强制单通道编码，避免无效的流映射
        $format->setPasses(1);
        $format->setKiloBitrate(0);  // 触发单通道模式
        $format->setAudioKiloBitrate(128);

        // 🔴 修复流映射错误：使用更安全的参数组合
        $additionalParams = [
            // 直接复制视频和音频流，避免重新编码
            '-c:v',
            'copy',
            '-c:a',
            'copy',
            // 显式映射所有可用流，避免 'f' 映射错误
            '-map',
            '0:v:0?',  // 映射第一个视频流，? 表示如果不存在则忽略
            '-map',
            '0:a:0?',  // 映射第一个音频流，? 表示如果不存在则忽略
            // 输出格式：使用 mpegts 以获得更好的流兼容性
            '-f',
            'mpegts',
            // 避免不必要的元数据和字幕
            '-avoid_negative_ts',
            'make_zero',
            // 设置适当的缓冲
            '-max_muxing_queue_size',
            '1024'
        ];
        $format->setAdditionalParameters($additionalParams);

        return $format;
    }

    /**
     * 创建备用格式配置（如果主配置失败）
     *
     * 使用最简单的参数，确保兼容性
     */
    private function createFallbackFormat(): X264
    {
        $format = new X264('aac', 'libx264');

        // 最基本的单通道设置
        $format->setPasses(1);
        $format->setKiloBitrate(0);
        $format->setAudioKiloBitrate(128);

        // 最简单的流复制配置
        $additionalParams = [
            '-c',
            'copy',  // 复制所有流
            '-f',
            'mp4'    // 使用兼容性更好的mp4格式
        ];
        $format->setAdditionalParameters($additionalParams);

        return $format;
    }

    /**
     * 验证FFmpeg命令参数
     *
     * 检查命令中是否包含可能导致错误的参数
     */
    private function validateCommand(array $command): array
    {
        $validatedCommand = [];
        $skipNext = false;

        foreach ($command as $i => $param) {
            if ($skipNext) {
                $skipNext = false;
                continue;
            }

            // 检查并修复问题参数
            if ($param === '-map') {
                $nextParam = $command[$i + 1] ?? '';

                // 如果下一个参数是 'f' 或其他无效值，则跳过这个映射
                if ($nextParam === 'f' || empty($nextParam) || !preg_match('/^[0-9:?]+$/', $nextParam)) {
                    $skipNext = true;
                    error_log("PhpFFMpegRecorder: 跳过无效的流映射参数: -map {$nextParam}");
                    continue;
                }
            }

            $validatedCommand[] = $param;
        }

        return $validatedCommand;
    }

    /**
     * 调试：打印FFmpeg命令
     */
    private function debugCommand(array $command, string $context = ''): void
    {
        $commandString = implode(' ', $command);
        error_log("PhpFFMpegRecorder Debug [{$context}]: {$commandString}");
    }

    /**
     * 解析命令字符串为数组
     *
     * 确保返回的命令数组以 "ffmpeg" 开头，并正确处理带引号的参数
     */
    private function parseCommandString(string $commandString): array
    {
        // 使用更健壮的命令解析方法
        $parts = $this->parseCommandParts($commandString);
        $filteredParts = array_filter($parts, fn($part) => !empty(trim($part)));

        // 确保命令以 "ffmpeg" 开头
        if (empty($filteredParts) || $filteredParts[0] !== 'ffmpeg') {
            array_unshift($filteredParts, 'ffmpeg');
        }

        return array_values($filteredParts);
    }

    /**
     * 更健壮的命令参数解析
     *
     * 支持带引号的参数和转义字符
     */
    private function parseCommandParts(string $commandString): array
    {
        $parts = [];
        $current = '';
        $inQuotes = false;
        $quoteChar = null;
        $length = strlen($commandString);

        for ($i = 0; $i < $length; $i++) {
            $char = $commandString[$i];

            if (!$inQuotes && ($char === '"' || $char === "'")) {
                $inQuotes = true;
                $quoteChar = $char;
            } elseif ($inQuotes && $char === $quoteChar) {
                $inQuotes = false;
                $quoteChar = null;
            } elseif (!$inQuotes && $char === ' ') {
                if ($current !== '') {
                    $parts[] = $current;
                    $current = '';
                }
            } else {
                $current .= $char;
            }
        }

        if ($current !== '') {
            $parts[] = $current;
        }

        return $parts;
    }
}
