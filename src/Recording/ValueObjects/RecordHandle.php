<?php

declare(strict_types=1);

namespace LiveStream\Recording\ValueObjects;

use Symfony\Component\Process\Process;

/**
 * 录制句柄值对象
 *
 * 封装录制进程的控制和状态信息，提供统一的接口用于管理录制会话
 */
final class RecordHandle
{
    /**
     * 录制句柄构造函数
     *
     * @param string $recordId 录制唯一标识
     * @param string $outputPath 输出文件路径
     * @param array $command 执行的FFmpeg命令数组
     * @param Process|null $process 关联的进程对象（同步录制时为null）
     */
    public function __construct(
        private readonly string $recordId,
        private readonly string $outputPath,
        private readonly array $command,
        private readonly ?Process $process = null
    ) {}

    /**
     * 获取录制ID
     */
    public function getRecordId(): string
    {
        return $this->recordId;
    }

    /**
     * 获取输出文件路径
     */
    public function getOutputPath(): string
    {
        return $this->outputPath;
    }

    /**
     * 获取执行的命令数组
     */
    public function getCommand(): array
    {
        return $this->command;
    }

    /**
     * 获取关联的进程对象
     */
    public function getProcess(): ?Process
    {
        return $this->process;
    }

    /**
     * 检查是否为异步录制
     */
    public function isAsync(): bool
    {
        return $this->process !== null;
    }

    /**
     * 检查进程是否正在运行
     */
    public function isRunning(): bool
    {
        return $this->process?->isRunning() ?? false;
    }

    /**
     * 获取进程状态描述
     */
    public function getStatus(): string
    {
        if ($this->process === null) {
            return 'completed';
        }

        if ($this->process->isRunning()) {
            return 'running';
        }

        if ($this->process->isSuccessful()) {
            return 'completed';
        }

        return 'failed';
    }

    /**
     * 停止录制进程
     */
    public function stop(?int $signal = null): bool
    {
        if ($this->process === null || !$this->process->isRunning()) {
            return false;
        }

        $exitCode = $this->process->stop(timeout: 10, signal: $signal);
        return $exitCode === 0 || $exitCode === null;
    }

    /**
     * 强制终止录制进程
     */
    public function kill(?int $signal = null): bool
    {
        if ($this->process === null) {
            return false;
        }

        $exitCode = $this->process->stop(timeout: 0, signal: $signal ?? 9);
        return $exitCode === 0 || $exitCode === null;
    }

    /**
     * 获取进程输出
     */
    public function getOutput(): string
    {
        return $this->process?->getOutput() ?? '';
    }

    /**
     * 获取进程错误输出
     */
    public function getErrorOutput(): string
    {
        return $this->process?->getErrorOutput() ?? '';
    }

    /**
     * 获取进程退出码
     */
    public function getExitCode(): ?int
    {
        return $this->process?->getExitCode();
    }
}
