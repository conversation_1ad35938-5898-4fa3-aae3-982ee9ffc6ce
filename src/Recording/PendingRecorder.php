<?php

declare(strict_types=1);

namespace LiveStream\Recording;

use LiveStream\Contracts\PlatformInterface;
use LiveStream\Config\RecordingOptions;
use LiveStream\Exceptions\LiveStreamException;
use LiveStream\Recording\RecordrConnector;
use LiveStream\Traits\HasRecordId;
use LiveStream\Traits\HasPathBuilder;
use LiveStream\Traits\HasStreamUrl;
use LiveStream\Contracts\LiveInterface;
use LiveStream\Traits\HasQuality;
use InvalidArgumentException;

class PendingRecorder
{
    use HasRecordId;
    use HasPathBuilder;
    use HasStreamUrl;
    use HasQuality;

    private ?LiveInterface $live = null;

    protected ?string $savePath = null;

    /**
     * 构造函数
     * 
     * @param PlatformInterface $platform 平台接口
     * @param RecordingOptions $options 录制选项
     * @param string $recordId 录制ID
     * @param bool $enableOverseasOptimization 是否启用海外优化
     * @throws LiveStreamException 当配置构建失败时
     */
    public function __construct(
        private readonly RecordrConnector $recordrConnector,
        private readonly PlatformInterface $platform,
        private readonly bool $enableOverseasOptimization = false,
    ) {}

    /**
     * 获取 RecordrConnector 实例
     * 
     * @return RecordrConnector  RecordrConnector 实例
     */
    public function recordrConnector(): RecordrConnector
    {
        return $this->recordrConnector;
    }

    /**
     * 获取房间信息
     * 
     * @return \LiveStream\Contracts\LiveInterface 房间信息
     */
    public function getLive(): LiveInterface
    {
        return $this->platform->getLive();
    }

    /**
     * 获取平台接口
     * 
     * @return PlatformInterface 平台接口
     */
    public function getPlatform(): PlatformInterface
    {
        return $this->platform;
    }

    /**
     * 是否启用海外优化
     * 
     * @return bool 是否启用海外优化
     */
    public function isOverseasOptimized(): bool
    {
        return $this->enableOverseasOptimization;
    }

    /**
     * 构建输出路径
     * 
     * @return string 输出路径
     * @throws LiveStreamException 当路径构建失败时
     */
    public function savePath(): string
    {
        return $this->savePath ??= $this->buildFilePath();
    }

    /**
     * 实现HasStreamUrl trait需要的方法：获取Live对象
     *
     * 重用现有的public getLive方法
     */

    /**
     * 实现HasStreamUrl trait需要的方法：获取配置对象
     *
     * @return RecordingOptions 配置对象
     */
    public function getConfig(): RecordingOptions
    {
        return $this->recordrConnector()->config();
    }


    /**
     * 构建文件路径
     *
     * @return string 文件路径
     */
    protected function buildFilePath(): string
    {
        $basePath = $this->recordrConnector()->config()->getSavePath();
        $anchorName = $this->sanitizeFilename($this->getLive()->getAnchorName());
        $title = $this->sanitizeFilename($this->getLive()->getTitle());
        $format = $this->recordrConnector()->config()->getFormat()->value;

        $timestamp = date('Y-m-d_H-i-s');
        $date = date('Y-m-d');

        // 使用新的安全路径构建方法
        $filePath = $this->buildSafePath(
            $basePath,
            $anchorName,
            $date,
            $title,
            "{$timestamp}.{$format}"
        );

        // 确保输出目录存在
        $this->ensureOutputDirectoryExists($filePath);

        return $filePath;
    }
}
