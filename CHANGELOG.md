# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Added
- Added comprehensive exception hierarchy for better error handling
  - `BaseException` - Base class for all LiveStream exceptions with context support
  - `RequestException` - Base class for retryable exceptions
  - `FatalRequestException` - Base class for non-retryable fatal exceptions
  - Specific exception classes for different error scenarios (NetworkTimeout, StreamNotLive, etc.)
- Added HasRetry trait to RecordrConnector for improved reliability (inspired by Saloon's HasTries)
  - `withTries()` - Configure maximum retry attempts
  - `withRetryInterval()` - Set delay between retry attempts (in milliseconds)
  - `withExponentialBackoff()` - Enable exponential backoff for retry delays
  - `withShouldRetry()` - Custom callback to determine if an exception should trigger a retry
- Added comprehensive unit tests for retry functionality
- Added examples demonstrating retry configuration and new exception handling

### Changed
- Refactored RecordrConnector handle method to follow <PERSON><PERSON>'s send pattern
  - Creates new PendingRecorder instance on each retry
  - Re-runs middleware pipeline on each attempt
  - Properly distinguishes between fatal and retryable exceptions
- RecordingException now extends RequestException and is deprecated
- RecordingException static factory methods now return specific exception types
- StreamConfig class is no longer marked as `final` for easier testing
- ValidateOptionsPipe now throws StreamNotLiveException instead of RecordingException

### Fixed
- Fixed RecordingOptions make() method parameter passing
- Fixed withConfig() method in HasConfig trait

### Deprecated
- RecordingException - Use specific exception classes instead
- RecordingException static factory methods - Use specific exception classes directly

## [Previous versions]

Previous changelog entries can be added here as the project evolves.
