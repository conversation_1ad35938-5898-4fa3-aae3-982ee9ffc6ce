<?php

declare(strict_types=1);

namespace Examples;

require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/config/ProcessRecordingConfig.php';
require_once __DIR__ . '/ProcessPoolManager.php';

use Examples\Config\ProcessRecordingConfig;
use Examples\ProcessPoolManager;
use LiveStream\PlatformFactory;
use LiveStream\Platforms\PlatformManager;
use LiveStream\Config\RecordingOptions;
use LiveStream\Recording\RecordrConnector;
use LiveStream\Recording\Pipes\DebugInfoPipe;
use Symfony\Component\Process\Process;

/**
 * 进程录制管理器
 * 
 * 使用 Symfony Process 组件并发处理多个直播流录制任务，
 * 替代 Swoole 协程实现，提供完善的错误处理、资源管理和监控功能。
 */
class ProcessRecordingManager
{
    private array $config;
    private PlatformManager $platformManager;
    private ProcessPoolManager $processPool;
    private array $statistics = [
        'total_tasks' => 0,
        'completed_tasks' => 0,
        'failed_tasks' => 0,
        'start_time' => 0,
        'memory_peak' => 0,
    ];

    /**
     * 监控进程
     */
    private ?Process $monitorProcess = null;

    /**
     * 是否正在运行
     */
    private bool $isRunning = false;

    /**
     * 构造函数
     * 
     * @param array $config 配置数组
     */
    public function __construct(array $config = [])
    {
        $this->config = ProcessRecordingConfig::mergeConfig($config);
        ProcessRecordingConfig::validateConfig($this->config);

        // 创建共享的平台管理器
        $this->platformManager = new PlatformManager(new PlatformFactory());

        // 创建进程池管理器
        $this->processPool = new ProcessPoolManager(
            $this->config['process']['max_processes'],
            $this->config['process']['process_timeout'],
            $this->config['process']['poll_interval']
        );

        $this->statistics['start_time'] = microtime(true);

        $this->log('info', 'ProcessRecordingManager 初始化完成', [
            'max_processes' => $this->config['process']['max_processes'],
            'total_urls' => count($this->config['urls']),
        ]);
    }

    /**
     * 开始录制任务
     *
     * @return void
     */
    public function startRecording(): void
    {
        $urls = $this->config['urls'];
        $this->statistics['total_tasks'] = count($urls);
        $this->isRunning = true;

        $this->log('info', '开始录制任务', [
            'total_urls' => count($urls),
            'max_processes' => $this->config['process']['max_processes'],
        ]);

        // 启动监控
        if ($this->config['process']['enable_monitoring']) {
            $this->startMonitoring();
        }

        // 分批处理URL，每批数量不超过 max_processes
        $maxProcesses = $this->config['process']['max_processes'];
        $batches = array_chunk($urls, $maxProcesses);

        $this->log('info', '分批处理URL', [
            'total_batches' => count($batches),
            'batch_size' => $maxProcesses,
        ]);

        // 逐批处理
        foreach ($batches as $batchIndex => $batch) {
            if (!$this->isRunning) {
                break;
            }
            $this->processBatch($batch, $batchIndex + 1);
        }

        // 等待所有进程完成
        $this->processPool->waitForAll(function ($stats) {
            $this->updateStatisticsFromPool($stats);
        });

        $this->isRunning = false;
        $this->stopMonitoring();
        $this->logFinalStatistics();
    }

    /**
     * 处理一批URL
     *
     * @param array $urls URL数组
     * @param int $batchIndex 批次索引
     * @return void
     */
    private function processBatch(array $urls, int $batchIndex): void
    {
        $this->log('info', "开始处理批次 {$batchIndex}", [
            'batch_size' => count($urls),
        ]);

        // 为当前批次的每个 URL 创建进程
        foreach ($urls as $index => $url) {
            if (!$this->isRunning) {
                break;
            }

            $identifier = "batch_{$batchIndex}_url_{$index}";
            $this->createUrlProcess($url, $identifier);
        }

        $this->log('info', "批次 {$batchIndex} 进程已全部启动");
    }

    /**
     * 为单个 URL 创建进程
     *
     * @param string $url 直播URL
     * @param string $identifier 进程标识符
     * @return void
     */
    private function createUrlProcess(string $url, string $identifier): void
    {
        // 创建 PHP 脚本来处理单个 URL
        $scriptPath = $this->createUrlProcessScript($url, $identifier);

        // 创建进程
        $process = new Process([
            'php',
            $scriptPath,
            $url,
            $identifier,
            json_encode($this->config)
        ]);

        $this->log('info', "创建URL处理进程", [
            'url' => $this->maskUrl($url),
            'identifier' => $identifier,
        ]);

        // 添加到进程池
        $this->processPool->addProcess($process, $identifier);
    }

    /**
     * 创建URL处理脚本
     *
     * @param string $url 直播URL
     * @param string $identifier 进程标识符
     * @return string 脚本路径
     */
    private function createUrlProcessScript(string $url, string $identifier): string
    {
        $scriptContent = $this->generateUrlProcessScript();
        $scriptPath = sys_get_temp_dir() . "/url_process_{$identifier}.php";

        file_put_contents($scriptPath, $scriptContent);

        return $scriptPath;
    }

    /**
     * 生成URL处理脚本内容
     *
     * 优化版本：使用更清晰的模板和更好的错误处理
     *
     * @return string 脚本内容
     */
    private function generateUrlProcessScript(): string
    {
        // 使用 heredoc 语法提高可读性
        return <<<'PHP_SCRIPT'
<?php
/**
 * 自动生成的URL处理脚本
 * 此脚本由 ProcessRecordingManager 动态创建
 */

// 错误报告设置
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 参数验证
if ($argc < 4) {
    fwrite(STDERR, "Usage: php script.php <url> <identifier> <config_json>\n");
    exit(1);
}

$url = $argv[1];
$identifier = $argv[2];
$configJson = $argv[3];

// 验证参数
if (empty($url)) {
    fwrite(STDERR, "Error: URL cannot be empty\n");
    exit(1);
}

// 解析配置
$config = json_decode($configJson, true);
if (json_last_error() !== JSON_ERROR_NONE) {
    fwrite(STDERR, "Error: Invalid JSON config: " . json_last_error_msg() . "\n");
    exit(1);
}

// 尝试加载 autoloader
$autoloaderPaths = [
    __DIR__ . '/../vendor/autoload.php',
    __DIR__ . '/../../vendor/autoload.php',
    __DIR__ . '/../../../vendor/autoload.php',
];

$autoloaderLoaded = false;
foreach ($autoloaderPaths as $path) {
    if (file_exists($path)) {
        require_once $path;
        $autoloaderLoaded = true;
        break;
    }
}

if (!$autoloaderLoaded) {
    fwrite(STDERR, "Error: Could not find autoloader\n");
    exit(1);
}

// 导入必要的类
use LiveStream\PlatformFactory;
use LiveStream\Platforms\PlatformManager;
use LiveStream\Config\RecordingOptions;
use LiveStream\Recording\RecordrConnector;
use LiveStream\Recording\Pipes\DebugInfoPipe;

try {
    // 记录开始时间
    $startTime = microtime(true);
    echo "[Process {$identifier}] Starting recording for URL: " . parse_url($url, PHP_URL_HOST) . "\n";

    // 创建平台管理器
    $platformManager = new PlatformManager(new PlatformFactory());
    $platform = $platformManager->driver($url);

    // 创建录制配置
    $options = new RecordingOptions();
    $options->setSavePath($config['recording']['save_path']);
    $options->setFormat($config['recording']['format']);
    $options->set([
        'timeout' => $config['recording']['timeout'],
        'max_retries' => $config['recording']['max_retries'],
        'custom_headers' => $config['recording']['custom_headers'],
    ]);

    // 创建录制连接器
    $recordrConnector = new RecordrConnector();
    $recordrConnector->withConfig($options);

    // 配置调试
    if (!empty($config['process']['enable_debug'])) {
        $recordrConnector->middleware()->pipe(new DebugInfoPipe());
        $recordrConnector->withDebug(true);
    }

    // 配置重试
    $recordrConnector
        ->withTries($config['recording']['max_retries'])
        ->withRetryInterval($config['recording']['retry_interval'])
        ->withExponentialBackoff($config['recording']['exponential_backoff']);

    // 执行录制
    $result = $recordrConnector->handle($platform, function (string $type, string $buffer) use ($identifier) {
        $trimmedBuffer = trim($buffer);
        if (!empty($trimmedBuffer)) {
            echo "[Process {$identifier}][FFmpeg {$type}]: {$trimmedBuffer}\n";
        }
    });

    $endTime = microtime(true);
    $duration = round($endTime - $startTime, 2);
    echo "[Process {$identifier}] Recording completed successfully in {$duration}s\n";

    exit(0);

} catch (\Throwable $e) {
    $endTime = microtime(true);
    $duration = round($endTime - $startTime, 2);

    fwrite(STDERR, "[Process {$identifier}] Recording failed after {$duration}s\n");
    fwrite(STDERR, "[Process {$identifier}] Error: " . $e->getMessage() . "\n");
    fwrite(STDERR, "[Process {$identifier}] File: " . $e->getFile() . ":" . $e->getLine() . "\n");

    // 在调试模式下输出完整堆栈跟踪
    if (!empty($config['process']['enable_debug'])) {
        fwrite(STDERR, "[Process {$identifier}] Stack trace:\n" . $e->getTraceAsString() . "\n");
    }

    exit(1);
}
PHP_SCRIPT;
    }

    /**
     * 从进程池更新统计信息
     *
     * @param array $poolStats 进程池统计信息
     * @return void
     */
    private function updateStatisticsFromPool(array $poolStats): void
    {
        $this->statistics['completed_tasks'] = $poolStats['completed'];
        $this->statistics['failed_tasks'] = $poolStats['failed'];
    }

    /**
     * 启动监控
     *
     * @return void
     */
    private function startMonitoring(): void
    {
        $monitorScript = $this->createMonitorScript();

        $this->monitorProcess = new Process([
            'php',
            $monitorScript,
            json_encode($this->config),
            (string)getmypid() // 传递主进程ID
        ]);

        $this->monitorProcess->start();

        $this->log('info', '监控进程启动', [
            'monitor_pid' => $this->monitorProcess->getPid(),
        ]);
    }

    /**
     * 停止监控
     *
     * @return void
     */
    private function stopMonitoring(): void
    {
        if ($this->monitorProcess && $this->monitorProcess->isRunning()) {
            $this->monitorProcess->stop();
            $this->log('info', '监控进程已停止');
        }
    }

    /**
     * 创建监控脚本
     *
     * @return string 脚本路径
     */
    private function createMonitorScript(): string
    {
        $scriptContent = $this->generateMonitorScript();
        $scriptPath = sys_get_temp_dir() . "/monitor_process_" . getmypid() . ".php";

        file_put_contents($scriptPath, $scriptContent);

        return $scriptPath;
    }

    /**
     * 生成监控脚本内容
     *
     * @return string 脚本内容
     */
    private function generateMonitorScript(): string
    {
        return '<?php
$configJson = $argv[1] ?? "{}";
$parentPid = (int)($argv[2] ?? 0);

$config = json_decode($configJson, true);
if (!$config) {
    exit(1);
}

$interval = $config["process"]["monitoring_interval"] ?? 5;
$memoryLimit = $config["process"]["memory_limit"] ?? (100 * 1024 * 1024);

echo "[Monitor] 监控进程启动，父进程PID: {$parentPid}\n";

while (true) {
    // 检查父进程是否还在运行
    if ($parentPid > 0 && !posix_kill($parentPid, 0)) {
        echo "[Monitor] 父进程已退出，监控进程结束\n";
        break;
    }

    $currentMemory = memory_get_usage(true);

    // 内存清理
    if ($currentMemory > $memoryLimit) {
        gc_collect_cycles();
        $afterGc = memory_get_usage(true);
        echo "[Monitor] 执行内存清理: " .
             round($currentMemory / 1024 / 1024, 2) . "MB -> " .
             round($afterGc / 1024 / 1024, 2) . "MB\n";
    }

    // 输出监控信息
    echo "[Monitor] 内存使用: " . round($currentMemory / 1024 / 1024, 2) . "MB\n";

    sleep($interval);
}
';
    }

    /**
     * 创建录制选项
     *
     * @return RecordingOptions
     */
    private function createRecordingOptions(): RecordingOptions
    {
        $options = new RecordingOptions();
        $recordingConfig = $this->config['recording'];

        $options->setSavePath($recordingConfig['save_path']);
        $options->setFormat($recordingConfig['format']);

        $options->set([
            'timeout' => $recordingConfig['timeout'],
            'max_retries' => $recordingConfig['max_retries'],
            'custom_headers' => $recordingConfig['custom_headers'],
        ]);

        return $options;
    }

    /**
     * 判断是否应该重试
     *
     * @param \Throwable $exception 异常
     * @param int $attempt 尝试次数
     * @return bool 是否重试
     */
    private function shouldRetry(\Throwable $exception, int $attempt): bool
    {
        $exceptionClass = get_class($exception);

        // 不重试的异常类型
        if (in_array($exceptionClass, $this->config['no_retry_exceptions'])) {
            $this->log('info', "异常不重试", [
                'exception' => $exceptionClass,
                'message' => $exception->getMessage(),
                'attempt' => $attempt,
            ]);
            return false;
        }

        // 重试的异常类型
        if (in_array($exceptionClass, $this->config['retry_exceptions'])) {
            $this->log('info', "异常重试", [
                'exception' => $exceptionClass,
                'message' => $exception->getMessage(),
                'attempt' => $attempt,
            ]);
            return true;
        }

        // 默认不重试
        return false;
    }

    /**
     * 记录统计信息
     *
     * @return void
     */
    private function logStatistics(): void
    {
        $runtime = microtime(true) - $this->statistics['start_time'];
        $poolStats = $this->processPool->getStatistics();

        $this->log('info', '运行统计', [
            'runtime' => round($runtime, 2) . 's',
            'completed' => $this->statistics['completed_tasks'],
            'failed' => $this->statistics['failed_tasks'],
            'total' => $this->statistics['total_tasks'],
            'memory_current' => round(memory_get_usage(true) / 1024 / 1024, 2) . 'MB',
            'memory_peak' => round($this->statistics['memory_peak'] / 1024 / 1024, 2) . 'MB',
            'active_processes' => $poolStats['running'],
        ]);
    }

    /**
     * 记录最终统计信息
     *
     * @return void
     */
    private function logFinalStatistics(): void
    {
        $runtime = microtime(true) - $this->statistics['start_time'];

        $this->log('info', '录制任务完成', [
            'total_runtime' => round($runtime, 2) . 's',
            'completed_tasks' => $this->statistics['completed_tasks'],
            'failed_tasks' => $this->statistics['failed_tasks'],
            'total_tasks' => $this->statistics['total_tasks'],
            'success_rate' => $this->statistics['total_tasks'] > 0
                ? round($this->statistics['completed_tasks'] / $this->statistics['total_tasks'] * 100, 2) . '%'
                : '0%',
            'memory_peak' => round($this->statistics['memory_peak'] / 1024 / 1024, 2) . 'MB',
        ]);
    }

    /**
     * 停止录制任务
     *
     * @return void
     */
    public function stopRecording(): void
    {
        $this->isRunning = false;
        $this->processPool->terminateAll();
        $this->stopMonitoring();

        $this->log('info', '录制任务已停止');
    }

    /**
     * 获取统计信息
     *
     * @return array 统计信息数组
     */
    public function getStatistics(): array
    {
        $poolStats = $this->processPool->getStatistics();
        $this->updateStatisticsFromPool($poolStats);

        return $this->statistics;
    }

    /**
     * 记录日志
     *
     * @param string $level 日志级别
     * @param string $message 消息
     * @param array $context 上下文
     * @return void
     */
    private function log(string $level, string $message, array $context = []): void
    {
        if (!$this->config['logging']['enable']) {
            return;
        }

        $timestamp = date('Y-m-d H:i:s');
        $contextStr = '';

        if (!empty($context)) {
            if ($this->config['logging']['include_memory_usage']) {
                $context['memory'] = round(memory_get_usage() / 1024 / 1024, 2) . 'MB';
            }

            if ($this->config['logging']['include_process_id']) {
                $context['pid'] = getmypid();
            }

            $contextStr = ' ' . json_encode($context, JSON_UNESCAPED_UNICODE);
        }

        printf(
            $this->config['logging']['format'] . "\n",
            $timestamp,
            strtoupper($level),
            $message . $contextStr
        );
    }

    /**
     * 掩码URL（隐藏敏感信息）
     *
     * @param string $url 原始URL
     * @return string 掩码后的URL
     */
    private function maskUrl(string $url): string
    {
        $parsed = parse_url($url);
        $host = $parsed['host'] ?? 'unknown';
        $path = $parsed['path'] ?? '';

        return $host . $path . '?...';
    }

    /**
     * 清理临时文件
     *
     * @return void
     */
    public function cleanup(): void
    {
        // 清理进程池
        $this->processPool->cleanup();

        // 清理临时脚本文件
        $tempDir = sys_get_temp_dir();
        $pattern = $tempDir . "/url_process_*.php";
        foreach (glob($pattern) as $file) {
            unlink($file);
        }

        $pattern = $tempDir . "/monitor_process_*.php";
        foreach (glob($pattern) as $file) {
            unlink($file);
        }

        $this->log('info', '清理完成');
    }

    /**
     * 析构函数
     */
    public function __destruct()
    {
        $this->cleanup();
    }
}
