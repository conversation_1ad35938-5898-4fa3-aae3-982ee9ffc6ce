<?php

declare(strict_types=1);

/**
 * PhpFFMpegRecorder 阻塞特性测试脚本
 * 
 * 测试和验证不同录制器对协程的影响
 */

require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/SwooleRecordingManagerOptimized.php';

use Examples\SwooleRecordingManagerOptimized;
use Examples\Config\SwooleRecordingConfig;

echo "=== PhpFFMpegRecorder 阻塞特性分析测试 ===\n\n";

// 测试配置
$testUrls = [
    'https://live.douyin.com/test1?anchor_id=1',
    'https://live.douyin.com/test2?anchor_id=2', 
    'https://live.douyin.com/test3?anchor_id=3',
    'https://live.douyin.com/test4?anchor_id=4',
    'https://live.douyin.com/test5?anchor_id=5',
];

echo "📊 测试场景设置：\n";
echo "- URL数量: " . count($testUrls) . "\n";
echo "- 测试目标: 对比不同录制器的协程友好性\n\n";

// 测试1：PhpFFMpegRecorder（阻塞模式）
echo "🔴 测试1：PhpFFMpegRecorder（阻塞模式）\n";
echo "----------------------------------------\n";

$blockingConfig = [
    'urls' => $testUrls,
    'recording' => [
        'recorder_type' => 'php-ffmpeg', // 强制使用阻塞录制器
        'save_path' => '/tmp/test_blocking',
        'max_retries' => 1,
    ],
    'swoole' => [
        'max_coroutines' => 10, // 会被自动调整为5
        'enable_debug' => true,
        'enable_monitoring' => false,
    ],
    'logging' => [
        'enable' => true,
        'level' => 'info',
    ],
];

echo "配置分析：\n";
echo "- 录制器类型: PhpFFMpegRecorder\n";
echo "- 阻塞特性: ✅ 会阻塞协程\n";
echo "- 预期行为: 自动调整为串行处理\n";
echo "- 并发效果: ❌ 无真正并发\n\n";

try {
    echo "创建阻塞模式管理器...\n";
    $blockingManager = new SwooleRecordingManagerOptimized($blockingConfig);
    echo "✅ 管理器创建成功\n";
    echo "📝 注意观察日志中的警告信息和自动调整\n\n";
} catch (\Throwable $e) {
    echo "❌ 创建失败: " . $e->getMessage() . "\n\n";
}

// 测试2：NativeFFmpegRecorder（异步模式）
echo "🟢 测试2：NativeFFmpegRecorder（异步模式）\n";
echo "----------------------------------------\n";

$asyncConfig = [
    'urls' => $testUrls,
    'recording' => [
        'recorder_type' => 'native', // 强制使用异步录制器
        'save_path' => '/tmp/test_async',
        'max_retries' => 1,
    ],
    'swoole' => [
        'max_coroutines' => 3, // 真正的并发控制
        'enable_debug' => true,
        'enable_monitoring' => false,
    ],
    'logging' => [
        'enable' => true,
        'level' => 'info',
    ],
];

echo "配置分析：\n";
echo "- 录制器类型: NativeFFmpegRecorder\n";
echo "- 阻塞特性: ❌ 不会阻塞协程\n";
echo "- 预期行为: 真正的批次并发处理\n";
echo "- 并发效果: ✅ 有效并发\n\n";

try {
    echo "创建异步模式管理器...\n";
    $asyncManager = new SwooleRecordingManagerOptimized($asyncConfig);
    echo "✅ 管理器创建成功\n";
    echo "📝 注意观察批次处理和协程并发\n\n";
} catch (\Throwable $e) {
    echo "❌ 创建失败: " . $e->getMessage() . "\n\n";
}

// 测试3：自动检测模式
echo "🔵 测试3：自动检测模式\n";
echo "------------------------\n";

$autoConfig = [
    'urls' => $testUrls,
    'recording' => [
        // 不指定 recorder_type，让系统自动选择
        'save_path' => '/tmp/test_auto',
        'max_retries' => 1,
    ],
    'swoole' => [
        'max_coroutines' => 5,
        'enable_debug' => true,
        'enable_monitoring' => false,
    ],
    'logging' => [
        'enable' => true,
        'level' => 'info',
    ],
];

echo "配置分析：\n";
echo "- 录制器类型: auto（自动检测）\n";
echo "- 预期行为: 自动选择 NativeFFmpegRecorder\n";
echo "- 优化效果: ✅ 最佳性能\n\n";

try {
    echo "创建自动检测模式管理器...\n";
    $autoManager = new SwooleRecordingManagerOptimized($autoConfig);
    echo "✅ 管理器创建成功\n\n";
} catch (\Throwable $e) {
    echo "❌ 创建失败: " . $e->getMessage() . "\n\n";
}

// 性能对比分析
echo "=== 性能对比分析 ===\n";

echo "\n📈 **理论性能对比**：\n";
echo "| 录制器类型 | 协程友好性 | 并发能力 | 资源利用率 | 推荐度 |\n";
echo "|------------|------------|----------|------------|--------|\n";
echo "| PhpFFMpegRecorder | ❌ 阻塞 | ❌ 无并发 | ❌ 低 | 🔴 不推荐 |\n";
echo "| NativeFFmpegRecorder | ✅ 异步 | ✅ 真并发 | ✅ 高 | 🟢 推荐 |\n\n";

echo "🔍 **阻塞问题详细分析**：\n";
echo "1. **PhpFFMpegRecorder 的 \$media->save() 方法**\n";
echo "   - 完全同步阻塞，直到录制完成\n";
echo "   - 阻塞时长：整个直播时长（可能数小时）\n";
echo "   - 协程影响：无法切换，失去协程优势\n\n";

echo "2. **对 SwooleRecordingManager 的影响**\n";
echo "   - 批次处理失去意义\n";
echo "   - WaitGroup 必须等待所有录制完成\n";
echo "   - 无法实现真正的并发录制\n\n";

echo "3. **优化后的处理策略**\n";
echo "   - 自动检测录制器类型\n";
echo "   - 阻塞模式：自动切换为串行处理\n";
echo "   - 异步模式：保持批次并发处理\n";
echo "   - 智能调整并发数量\n\n";

echo "⚡ **实际执行模拟**：\n";
echo "假设有5个URL，max_coroutines=3：\n\n";

echo "PhpFFMpegRecorder（阻塞模式）：\n";
echo "```\n";
echo "时间轴: 0----1----2----3----4----5小时\n";
echo "URL1:   [████████████████████████████] 阻塞5小时\n";
echo "URL2:                                [████████████████████████████] 阻塞5小时\n";
echo "URL3:                                                                [████████████████████████████]\n";
echo "URL4:   等待...\n";
echo "URL5:   等待...\n";
echo "总时长: 约25小时（串行执行）\n";
echo "```\n\n";

echo "NativeFFmpegRecorder（异步模式）：\n";
echo "```\n";
echo "时间轴: 0----1----2----3----4----5小时\n";
echo "URL1:   [████████████████████████████] 并发录制\n";
echo "URL2:   [████████████████████████████] 并发录制\n";
echo "URL3:   [████████████████████████████] 并发录制\n";
echo "URL4:                                [████████████████████████████] 第二批\n";
echo "URL5:                                [████████████████████████████] 第二批\n";
echo "总时长: 约10小时（并发执行）\n";
echo "```\n\n";

echo "🎯 **优化建议总结**：\n";
echo "1. ✅ **立即切换到 NativeFFmpegRecorder**\n";
echo "2. ✅ **使用优化版 SwooleRecordingManagerOptimized**\n";
echo "3. ✅ **合理设置 max_coroutines（建议10-50）**\n";
echo "4. ✅ **启用自动检测和智能调整**\n";
echo "5. ✅ **监控内存使用和协程状态**\n\n";

echo "🚨 **重要提醒**：\n";
echo "- PhpFFMpegRecorder 在 Swoole 环境下会严重影响性能\n";
echo "- 建议在生产环境中完全禁用 PhpFFMpegRecorder\n";
echo "- 如果必须使用，请使用串行处理模式\n";
echo "- 优化版管理器会自动处理这些问题\n\n";

echo "✅ 阻塞特性分析测试完成！\n";
echo "📝 建议查看生成的日志，了解不同模式的执行差异。\n";
