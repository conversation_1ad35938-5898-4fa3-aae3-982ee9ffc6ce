<?php

declare(strict_types=1);

require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/OptimizedPhpFFMpegRecorder.php';

use Examples\OptimizedPhpFFMpegRecorder;
use Examples\AsyncPhpFFMpegRecorder;
use Examples\PhpFFMpegCommandExtractor;
use LiveStream\Recording\PendingRecorder;
use LiveStream\Recording\RecordrConnector;
use LiveStream\Config\RecordingOptions;
use LiveStream\Streams\StreamUrl;

/**
 * PhpFFMpegRecorder 优化示例
 * 
 * 展示如何解决双通道编码和同步阻塞问题
 */

echo "=== PhpFFMpegRecorder 优化示例 ===\n\n";

// 模拟配置
$testUrl = 'https://live.douyin.com/test123';
$outputPath = '/tmp/test_recording.mp4';

try {
    // 1. 命令提取示例
    echo "1. 命令提取示例\n";
    echo "---------------\n";
    
    echo "提取优化后的 FFmpeg 命令...\n";
    $command = PhpFFMpegCommandExtractor::extractCommand(
        $testUrl,
        $outputPath,
        [],
        [
            'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Referer' => 'https://www.douyin.com/'
        ]
    );
    
    echo "生成的命令:\n";
    echo implode(' ', $command) . "\n\n";
    
    // 对比简化命令
    echo "简化的流复制命令:\n";
    $simpleCommand = PhpFFMpegCommandExtractor::buildStreamCopyCommand(
        $testUrl,
        $outputPath,
        [
            'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Referer' => 'https://www.douyin.com/'
        ]
    );
    echo implode(' ', $simpleCommand) . "\n\n";
    
} catch (\Throwable $e) {
    echo "命令提取错误: " . $e->getMessage() . "\n\n";
}

try {
    // 2. 性能对比示例
    echo "2. 性能对比示例\n";
    echo "---------------\n";
    
    // 创建模拟的 PendingRecorder
    $options = new RecordingOptions();
    $options->setSavePath('/tmp');
    
    $connector = new RecordrConnector();
    $connector->withConfig($options);
    
    $streamUrl = new StreamUrl($testUrl);
    $pendingRecorder = new PendingRecorder(
        recordId: 'test-' . time(),
        stream: $streamUrl,
        recordrConnector: $connector
    );
    
    // 模拟原版 PhpFFMpegRecorder 的问题
    echo "原版问题分析:\n";
    echo "- 使用 X264 格式，默认 getPasses() = 2\n";
    echo "- 会生成两个命令（pass 1 和 pass 2）\n";
    echo "- 每个命令都会录制完整的直播流\n";
    echo "- 总时间 = 2 × 直播时长\n";
    echo "- 网络带宽 = 2 × 正常带宽\n\n";
    
    // 优化版本的改进
    echo "优化版本改进:\n";
    echo "- 强制设置 setPasses(1)\n";
    echo "- 设置 setKiloBitrate(0) 触发单通道\n";
    echo "- 只生成一个命令\n";
    echo "- 总时间 = 1 × 直播时长\n";
    echo "- 网络带宽 = 1 × 正常带宽\n";
    echo "- 性能提升: 50%\n\n";
    
} catch (\Throwable $e) {
    echo "性能对比错误: " . $e->getMessage() . "\n\n";
}

try {
    // 3. 异步执行示例
    echo "3. 异步执行示例\n";
    echo "---------------\n";
    
    echo "创建异步 PhpFFMpegRecorder...\n";
    $asyncRecorder = new AsyncPhpFFMpegRecorder();
    
    echo "启动异步录制（模拟）...\n";
    // 注意：这里只是演示，实际使用时会启动真实的录制进程
    
    $progressCallback = function (string $type, string $buffer) {
        echo "[{$type}] " . trim($buffer) . "\n";
    };
    
    // 模拟异步启动
    echo "异步录制的优势:\n";
    echo "- 立即返回 RecordHandle\n";
    echo "- 不阻塞当前线程/协程\n";
    echo "- 可以并发处理多个录制任务\n";
    echo "- 支持进度回调\n";
    echo "- 可以随时停止或查询状态\n\n";
    
} catch (\Throwable $e) {
    echo "异步执行错误: " . $e->getMessage() . "\n\n";
}

try {
    // 4. 格式配置对比
    echo "4. 格式配置对比\n";
    echo "---------------\n";
    
    echo "原版配置问题:\n";
    echo "```php\n";
    echo "\$format = new X264('aac', 'libx264');\n";
    echo "\$format->setAudioKiloBitrate(128);\n";
    echo "// 默认 getPasses() 返回 2\n";
    echo "// 导致双通道编码\n";
    echo "```\n\n";
    
    echo "优化后配置:\n";
    echo "```php\n";
    echo "\$format = new X264('aac', 'libx264');\n";
    echo "\$format->setPasses(1);           // 强制单通道\n";
    echo "\$format->setKiloBitrate(0);      // 触发单通道模式\n";
    echo "\$format->setAudioKiloBitrate(128);\n";
    echo "\$format->setAdditionalParameters([\n";
    echo "    '-c:v', 'copy',              // 视频流复制\n";
    echo "    '-c:a', 'copy',              // 音频流复制\n";
    echo "    '-map', '0',\n";
    echo "    '-f', 'mpegts'\n";
    echo "]);\n";
    echo "```\n\n";
    
} catch (\Throwable $e) {
    echo "格式配置错误: " . $e->getMessage() . "\n\n";
}

try {
    // 5. 实际命令对比
    echo "5. 实际命令对比\n";
    echo "---------------\n";
    
    echo "原版生成的命令（双通道）:\n";
    echo "Pass 1: ffmpeg -y -i {url} -vcodec libx264 -acodec aac -b:v 1000k ... -c:v copy -c:a copy -pass 1 -passlogfile /tmp/pass-xxx {output}\n";
    echo "Pass 2: ffmpeg -y -i {url} -vcodec libx264 -acodec aac -b:v 1000k ... -c:v copy -c:a copy -pass 2 -passlogfile /tmp/pass-xxx {output}\n\n";
    
    echo "优化后生成的命令（单通道）:\n";
    echo "Single: ffmpeg -y -i {url} -c:v copy -c:a copy -map 0 -f mpegts -avoid_negative_ts make_zero {output}\n\n";
    
    echo "命令分析:\n";
    echo "- 移除了不必要的编码参数（-vcodec libx264 -acodec aac -b:v 1000k）\n";
    echo "- 移除了双通道参数（-pass 1/2 -passlogfile）\n";
    echo "- 保留了流复制参数（-c:v copy -c:a copy）\n";
    echo "- 添加了时间戳处理（-avoid_negative_ts make_zero）\n";
    echo "- 命令更简洁，执行更高效\n\n";
    
} catch (\Throwable $e) {
    echo "命令对比错误: " . $e->getMessage() . "\n\n";
}

echo "=== 优化总结 ===\n";
echo "\n关键优化点:\n";
echo "1. **解决双通道编码问题**:\n";
echo "   - 强制设置 setPasses(1)\n";
echo "   - 设置 setKiloBitrate(0)\n";
echo "   - 减少 50% 的录制时间和带宽消耗\n\n";

echo "2. **解决同步阻塞问题**:\n";
echo "   - 使用 getFinalCommand() 提取命令\n";
echo "   - 通过 Symfony Process 异步执行\n";
echo "   - 立即返回可控制的 RecordHandle\n\n";

echo "3. **保持兼容性**:\n";
echo "   - 相同的接口和方法签名\n";
echo "   - 相同的错误处理机制\n";
echo "   - 相同的进度回调支持\n\n";

echo "4. **性能提升**:\n";
echo "   - 录制时间减少 50%\n";
echo "   - 网络带宽减少 50%\n";
echo "   - 协程阻塞时间减少 50%\n";
echo "   - 支持真正的异步并发\n\n";

echo "5. **推荐使用方案**:\n";
echo "   - 短期: OptimizedPhpFFMpegRecorder（解决双通道问题）\n";
echo "   - 长期: AsyncPhpFFMpegRecorder（解决阻塞问题）\n";
echo "   - 最佳: 切换到 NativeFFmpegRecorder + ProcessRecordingManager\n\n";

echo "通过这些优化，PhpFFMpegRecorder 可以在保持功能完整性的同时，\n";
echo "显著提升性能并解决在 Swoole 协程环境中的阻塞问题。\n";
