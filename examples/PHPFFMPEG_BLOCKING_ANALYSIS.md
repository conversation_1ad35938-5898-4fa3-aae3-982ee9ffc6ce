# PhpFFMpegRecorder 阻塞特性分析与协程优化方案

## 1. PhpFFMpegRecorder 执行机制分析

### 1.1 关键代码分析

从 `src/Recording/Drivers/PhpFFMpegRecorder.php` 第65行的关键代码：

```php
// 5. 启动录制
// save 方法会阻塞执行，我们需要一种方式来异步管理它
// 为了与现有架构兼容，我们将在这里模拟一个进程句柄
$process = $media->save($format, $pendingRecorder->savePath());

// 因为 save() 是阻塞的，所以执行到这里时已经完成了。
// 这与 NativeFFmpegRecorder 的异步行为不同。
// 在实际应用中，这部分需要用消息队列等方式来异步执行。
```

### 1.2 阻塞特性确认

| 方面 | PhpFFMpegRecorder | NativeFFmpegRecorder |
|------|-------------------|----------------------|
| **执行方式** | 同步阻塞 | 异步非阻塞 |
| **save() 方法** | 阻塞直到录制完成 | 立即返回进程句柄 |
| **进程管理** | 内部管理，无法控制 | 返回可控制的进程对象 |
| **协程友好性** | ❌ 不友好 | ✅ 友好 |

### 1.3 底层实现机制

**PhpFFMpegRecorder 的执行流程：**
```
1. FFMpeg::create() - 创建FFmpeg实例
2. $ffmpeg->open() - 打开媒体流
3. $media->save() - 【阻塞点】同步执行录制
4. 录制完成后才返回
```

**NativeFFmpegRecorder 的执行流程：**
```
1. 构建FFmpeg命令
2. ProcessRunner->start() - 启动异步进程
3. 立即返回RecordHandle
4. 进程在后台运行
```

## 2. 对 Swoole 协程的影响评估

### 2.1 严重阻塞问题

**问题描述：**
- `$media->save()` 是完全同步的操作
- 会阻塞当前协程直到录制完成（可能数小时）
- 阻塞期间该协程无法处理其他任务

**影响范围：**
```php
// SwooleRecordingManager.php 中的协程
Coroutine::create(function () use ($url, $urlIndex, $waitGroup) {
    try {
        // 这里会调用 PhpFFMpegRecorder->start()
        $this->processUrl($url, $urlIndex);  // ❌ 长时间阻塞
    } finally {
        $waitGroup->done();
    }
});
```

### 2.2 批次处理失效

**当前批次处理逻辑：**
```php
// 分批处理URL，每批数量不超过 max_coroutines
$maxCoroutines = $this->config['swoole']['max_coroutines'];
$batches = array_chunk($urls, $maxCoroutines);

foreach ($batches as $batchIndex => $batch) {
    $this->processBatch($batch, $batchIndex + 1);  // ❌ 必须等待当前批次完成
}
```

**问题分析：**
- 每个协程都会被 `save()` 方法长时间阻塞
- `$waitGroup->wait()` 必须等待所有录制完成
- 下一批次无法开始，失去了并发优势

### 2.3 协程调度器影响

**Swoole 协程调度特点：**
- 协程在遇到 I/O 操作时会自动让出 CPU
- 但 `$media->save()` 是同步阻塞，不会触发协程切换
- 导致协程调度器无法有效工作

## 3. 潜在阻塞点识别

### 3.1 主要阻塞点

| 阻塞点 | 位置 | 阻塞时长 | 影响程度 |
|--------|------|----------|----------|
| **FFMpeg::create()** | 第24行 | 短暂 | 🟡 轻微 |
| **$ffmpeg->open()** | 第30行 | 短暂 | 🟡 轻微 |
| **$media->save()** | 第65行 | 数小时 | 🔴 严重 |

### 3.2 详细分析

#### 3.2.1 FFMpeg::create() - 轻微阻塞
```php
$ffmpeg = FFMpeg::create($pendingRecorder->recordrConnector()->config()->all());
```
- **阻塞原因**：初始化FFmpeg二进制文件检查
- **阻塞时长**：通常 < 100ms
- **影响评估**：可接受

#### 3.2.2 $ffmpeg->open() - 轻微阻塞
```php
$media = $ffmpeg->open($streamUrl->getUrl());
```
- **阻塞原因**：连接流媒体服务器，获取流信息
- **阻塞时长**：通常 1-5 秒
- **影响评估**：可接受

#### 3.2.3 $media->save() - 严重阻塞
```php
$process = $media->save($format, $pendingRecorder->savePath());
```
- **阻塞原因**：同步执行整个录制过程
- **阻塞时长**：直播时长（可能数小时）
- **影响评估**：❌ 完全不可接受

## 4. 优化方案设计

### 4.1 方案一：替换为 NativeFFmpegRecorder（推荐）

**实现方式：**
```php
// 在配置中指定使用 NativeFFmpegRecorder
$config = [
    'recording' => [
        'driver' => 'native', // 而不是 'php-ffmpeg'
    ],
];
```

**优势：**
- ✅ 完全异步，协程友好
- ✅ 可控制进程生命周期
- ✅ 实时获取录制进度
- ✅ 无需修改现有协程逻辑

### 4.2 方案二：异步包装 PhpFFMpegRecorder

**核心思路：**
```php
class AsyncPhpFFMpegRecorder implements RecorderInterface
{
    public function start(PendingRecorder $pendingRecorder, ?callable $progress = null): RecordHandle
    {
        // 1. 构建 FFmpeg 命令（而不是直接调用 save()）
        $command = $this->buildFFmpegCommand($pendingRecorder);
        
        // 2. 使用 Swoole\Process 异步启动
        $process = new \Swoole\Process(function () use ($command, $progress) {
            // 在子进程中执行录制
            exec(implode(' ', $command), $output, $returnCode);
            
            if ($progress) {
                foreach ($output as $line) {
                    $progress('stdout', $line);
                }
            }
        });
        
        $process->start();
        
        // 3. 返回可控制的句柄
        return new RecordHandle(
            recordId: $pendingRecorder->getRecordId(),
            outputPath: $pendingRecorder->getOutputPath(),
            command: $command,
            process: $process
        );
    }
    
    private function buildFFmpegCommand(PendingRecorder $pendingRecorder): array
    {
        // 构建与 php-ffmpeg 等效的命令
        return [
            'ffmpeg',
            '-i', $pendingRecorder->getStream()->getUrl(),
            '-c:v', 'copy',
            '-c:a', 'copy',
            '-f', 'mpegts',
            $pendingRecorder->savePath()
        ];
    }
}
```

### 4.3 方案三：进程池隔离

**实现思路：**
```php
class ProcessPoolPhpFFMpegRecorder implements RecorderInterface
{
    private \Swoole\Process\Pool $pool;
    
    public function __construct()
    {
        $this->pool = new \Swoole\Process\Pool(4); // 4个工作进程
        
        $this->pool->on('WorkerStart', function ($pool, $workerId) {
            // 在工作进程中初始化 php-ffmpeg
            while (true) {
                $task = $pool->getProcess()->pop(); // 阻塞等待任务
                $this->executeRecording($task);
            }
        });
        
        $this->pool->start();
    }
    
    public function start(PendingRecorder $pendingRecorder, ?callable $progress = null): RecordHandle
    {
        // 将任务推送到进程池
        $taskData = [
            'stream_url' => $pendingRecorder->getStream()->getUrl(),
            'output_path' => $pendingRecorder->savePath(),
            'config' => $pendingRecorder->recordrConnector()->config()->all(),
        ];
        
        $this->pool->getProcess()->push(json_encode($taskData));
        
        // 返回句柄（需要实现状态跟踪）
        return new RecordHandle(/* ... */);
    }
}
```

## 5. ProcessRecordingManager 的优势

### 5.1 天然的进程隔离

**ProcessRecordingManager 的处理方式：**
```php
// 每个录制任务在独立进程中运行
$process = new Process([
    'php',
    $scriptPath,
    $url,
    $identifier,
    json_encode($this->config)
]);

// ✅ 即使使用 PhpFFMpegRecorder，也不会阻塞主进程
$this->processPool->addProcess($process, $identifier);
```

### 5.2 对比分析

| 特性 | SwooleRecordingManager | ProcessRecordingManager |
|------|----------------------|------------------------|
| **PhpFFMpeg 兼容性** | ❌ 会阻塞协程 | ✅ 进程隔离，无阻塞 |
| **并发处理** | ❌ 受阻塞影响 | ✅ 真正的并发 |
| **错误隔离** | ❌ 可能影响其他协程 | ✅ 完全隔离 |
| **资源管理** | ❌ 内存泄漏风险 | ✅ 进程级别清理 |

## 6. 最终建议

### 6.1 短期解决方案

1. **立即切换到 NativeFFmpegRecorder**：
```php
// 在 SwooleRecordingManager 中
$config['recording']['driver'] = 'native';
```

2. **如果必须使用 PhpFFMpegRecorder**：
   - 切换到 ProcessRecordingManager
   - 或实现异步包装器

### 6.2 长期架构建议

1. **生产环境**：使用 NativeFFmpegRecorder + ProcessRecordingManager
2. **开发环境**：可以使用 PhpFFMpegRecorder + ProcessRecordingManager
3. **高并发场景**：避免使用 PhpFFMpegRecorder

### 6.3 关键要点

- **PhpFFMpegRecorder 的阻塞是架构性问题**，无法通过简单配置解决
- **Swoole 协程环境中使用 PhpFFMpegRecorder 会严重影响并发性能**
- **ProcessRecordingManager 天然解决了这个问题**，通过进程隔离避免阻塞
- **NativeFFmpegRecorder 是最佳选择**，既支持协程又支持进程模式
