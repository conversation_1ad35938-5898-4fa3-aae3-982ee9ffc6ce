# ProcessRecordingManager - Symfony Process 录制管理器

## 概述

`ProcessRecordingManager` 是 `SwooleRecordingManager` 的替代实现，使用 Symfony Process 组件替代 Swoole 协程来处理多个并发的直播流录制任务。

## 主要特性

- ✅ **完全替代 Swoole**: 使用 Symfony Process 组件，无需 Swoole 扩展
- ✅ **进程级隔离**: 每个录制任务在独立进程中运行，提供更好的稳定性
- ✅ **兼容原有接口**: 保持与 SwooleRecordingManager 相同的公共接口
- ✅ **配置自动转换**: 支持从 Swoole 配置自动转换
- ✅ **完整监控**: 提供进程监控、统计信息和资源管理
- ✅ **错误处理**: 完善的重试机制和异常处理
- ✅ **易于部署**: 标准 PHP 环境即可运行

## 文件结构

```
examples/
├── ProcessRecordingManager.php     # 主录制管理器
├── ProcessPoolManager.php          # 进程池管理器
├── config/
│   └── ProcessRecordingConfig.php  # 配置管理类
├── ProcessRecordingExample.php     # 使用示例
├── ProcessRecordingTest.php        # 功能测试
├── PROCESS_VS_SWOOLE.md           # 对比文档
└── README.md                       # 本文档
```

## 快速开始

### 1. 基本使用

```php
<?php
require_once 'ProcessRecordingManager.php';

use Examples\ProcessRecordingManager;

$config = [
    'urls' => [
        'https://live.douyin.com/123456789',
        'https://live.douyin.com/987654321',
    ],
    'process' => [
        'max_processes' => 5,
        'process_timeout' => 3600,
    ],
    'recording' => [
        'save_path' => '/path/to/recordings',
    ],
];

$manager = new ProcessRecordingManager($config);
$manager->startRecording();
```

### 2. 从 Swoole 配置迁移

```php
<?php
use Examples\Config\ProcessRecordingConfig;

// 原有的 Swoole 配置
$swooleConfig = [
    'swoole' => [
        'max_coroutines' => 10,
        'enable_debug' => true,
    ],
    // ... 其他配置
];

// 自动转换为进程配置
$processConfig = ProcessRecordingConfig::convertFromSwooleConfig($swooleConfig);
$manager = new ProcessRecordingManager($processConfig);
```

## 配置说明

### 进程配置 (process)

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `max_processes` | int | 10 | 最大并发进程数 |
| `process_timeout` | int | 3600 | 单个进程超时时间（秒） |
| `poll_interval` | int | 100000 | 轮询间隔（微秒） |
| `enable_monitoring` | bool | true | 是否启用监控 |
| `monitoring_interval` | int | 5 | 监控间隔（秒） |

### 录制配置 (recording)

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `save_path` | string | '/app/downloads' | 录制文件保存路径 |
| `format` | string | 'mp4' | 录制文件格式 |
| `max_retries` | int | 3 | 最大重试次数 |
| `retry_interval` | int | 1000 | 重试间隔（毫秒） |

### 日志配置 (logging)

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `enable` | bool | true | 是否启用日志 |
| `include_process_id` | bool | true | 是否包含进程ID |
| `include_memory_usage` | bool | true | 是否包含内存使用信息 |

## 运行示例

### 1. 运行功能测试

```bash
php examples/ProcessRecordingTest.php
```

### 2. 运行使用示例

```bash
php examples/ProcessRecordingExample.php
```

## 与 SwooleRecordingManager 对比

| 特性 | SwooleRecordingManager | ProcessRecordingManager |
|------|----------------------|------------------------|
| 依赖 | 需要 Swoole 扩展 | 仅需 Symfony Process |
| 并发模型 | 协程 | 进程 |
| 资源隔离 | 协程级别 | 进程级别 |
| 最大并发 | 1000+ | 100 以内 |
| 稳定性 | 中等 | 高 |
| 部署复杂度 | 高 | 低 |

详细对比请参考 [PROCESS_VS_SWOOLE.md](PROCESS_VS_SWOOLE.md)

## 最佳实践

### 1. 进程数配置

```php
// 根据服务器性能调整
$config['process']['max_processes'] = min(
    10,  // 最大不超过10个进程
    max(2, (int)(shell_exec('nproc') * 0.8))  // 80%的CPU核心数
);
```

### 2. 超时配置

```php
// 根据录制时长调整
$config['process']['process_timeout'] = 3600; // 1小时
$config['recording']['timeout'] = 30;         // 30秒连接超时
```

### 3. 监控配置

```php
// 生产环境建议启用监控
$config['process']['enable_monitoring'] = true;
$config['process']['monitoring_interval'] = 10; // 10秒间隔
```

## 故障排除

### 1. 进程启动失败

- 检查 PHP 路径是否正确
- 确认有足够的系统资源
- 查看错误日志

### 2. 录制失败

- 检查保存路径权限
- 验证 URL 有效性
- 查看重试配置

### 3. 性能问题

- 减少 `max_processes` 数量
- 增加 `poll_interval` 间隔
- 禁用不必要的监控

## 注意事项

1. **进程数限制**: 建议不超过 50 个并发进程
2. **内存使用**: 每个进程独立占用内存，注意总内存使用
3. **临时文件**: 系统会自动清理临时脚本文件
4. **信号处理**: 支持优雅停止，可通过 `stopRecording()` 方法停止

## 许可证

本项目遵循与主项目相同的 MIT 许可证。
