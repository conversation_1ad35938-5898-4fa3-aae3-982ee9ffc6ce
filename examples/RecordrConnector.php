<?php

require_once __DIR__ . '/../vendor/autoload.php';


use LiveStream\PlatformFactory;
use LiveStream\Platforms\PlatformManager;
use LiveStream\Config\RecordingOptions;
use LiveStream\Recording\RecordrConnector;


$platformManager = new PlatformManager(new PlatformFactory());

$platform = $platformManager->driver('https://live.douyin.com/94400148976?anchor_id=3340774829721161&follow_status=0&is_vs=0&vs_ep_group_id=&vs_episode_id=&vs_episode_stage=&vs_season_id=&web_live_tab_2=&web_live_tab_3=');

// 创建自定义的录制选项，指定保存路径
$options = new RecordingOptions();

$options->setSavePath('/app/downloads');
// $options->setQuality(\LiveStream\Enum\Quality::FULL_HD1);
$options->setFormat(\LiveStream\Enum\OutputFormat::MP4);
$options->set([
    'timeout' => 0,
    'max_retries' => 0,
    'custom_headers' => [
        'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Referer' => 'https://www.douyin.com/',
    ],
]);

$recordrConnector = new RecordrConnector();

$recordrConnector->withConfig($options);
$recordrConnector->withDebug(true);

$recordrConnector->withTries(tries: 3)                    // 最多重试3次
    ->withRetryInterval(milliseconds: 1000)          // 每次重试延迟1秒（1000毫秒）
    ->withExponentialBackoff(enabled: false);    // 启用指数退避

// 自定义重试判断逻辑
$recordrConnector->withShouldRetry(function (\Throwable $exception, int $attempt) {
    echo "捕获到异常 [尝试 {$attempt}]: " . get_class($exception) . " - " . $exception->getMessage() . "\n";

    // 根据异常类型决定是否重试
    if ($exception instanceof \Alchemy\BinaryDriver\Exception\ExecutionFailureException) {
        echo "  -> 重试\n";
        return true;
    }

    if ($exception instanceof \FFMpeg\Exception\RuntimeException) {
        echo "  -> 重试\n";
        return true;
    }

    return false;
});



$result = $recordrConnector->handleSync($platform, function (string $type, string $buffer) {
    echo "\n[FFmpeg $type]: {$buffer}" . trim($buffer);
});
