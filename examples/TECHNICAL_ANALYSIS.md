# 进程管理技术分析与最佳实践

## 1. Symfony Process 组件的执行能力

### 跨平台支持

Symfony Process 是完全跨平台的组件：

```php
// ✅ Linux/macOS
$process = new Process(['ls', '-la']);

// ✅ Windows  
$process = new Process(['dir']);

// ✅ 跨平台 PHP 执行
$process = new Process(['php', '-v']);
```

**支持的操作系统**：
- Linux (所有发行版)
- macOS
- Windows (7/8/10/11/Server)
- FreeBSD, OpenBSD 等 Unix 系统

### 执行方式限制

**核心限制**：Symfony Process 只能执行系统命令，不能直接执行 PHP 代码片段。

```php
// ❌ 不支持 - 直接执行 PHP 代码
$phpCode = 'echo "Hello World";';
// 没有 $process->executePhpCode($phpCode) 这样的方法

// ✅ 支持 - 执行 PHP 脚本文件
$process = new Process(['php', 'script.php']);

// ✅ 支持 - 使用 -r 参数执行简单代码
$process = new Process(['php', '-r', 'echo "Hello World";']);
```

**-r 参数的限制**：
```php
// ✅ 简单代码可以
$process = new Process(['php', '-r', 'echo date("Y-m-d");']);

// ❌ 复杂代码不行（无法加载 autoloader）
$process = new Process(['php', '-r', 'use MyNamespace\MyClass; $obj = new MyClass();']);
```

## 2. 当前实现中的脚本生成机制

### 为什么需要生成临时脚本？

**技术原因分析**：

1. **复杂依赖加载**：
```php
// 需要加载 Composer autoloader
require_once __DIR__ . '/../vendor/autoload.php';

// 需要使用复杂的类
use LiveStream\PlatformFactory;
use LiveStream\Platforms\PlatformManager;
```

2. **对象状态传递**：
```php
// 需要传递复杂配置对象
$platformManager = new PlatformManager(new PlatformFactory());
$options = new RecordingOptions();
$recordrConnector = new RecordrConnector();
```

3. **错误处理逻辑**：
```php
// 需要完整的 try-catch 逻辑
try {
    $recordrConnector->handle($platform, $callback);
} catch (\Throwable $e) {
    // 复杂的错误处理
}
```

### 当前方案的问题

```php
// 问题1：每次都创建临时文件
$scriptPath = sys_get_temp_dir() . "/url_process_{$identifier}.php";
file_put_contents($scriptPath, $scriptContent);

// 问题2：文件系统 I/O 开销
// 问题3：临时文件清理复杂
// 问题4：调试困难（文件动态生成）
```

## 3. Swoole 进程管理的对比

### Swoole Process 的优势

```php
use Swoole\Process;

// ✅ 直接传递闭包
$process = new Process(function (Process $worker) use ($url, $config) {
    // 可以直接访问父进程的变量和对象
    $platformManager = $this->platformManager;  // 直接访问
    $platform = $platformManager->driver($url);
    
    // 无需序列化/反序列化
    $recordrConnector = new RecordrConnector();
    $recordrConnector->withConfig($this->createRecordingOptions());
    
    // 直接执行，无需创建文件
    $recordrConnector->handle($platform);
});

$process->start();
```

### 技术对比表

| 特性 | Swoole Process | Symfony Process |
|------|----------------|-----------------|
| **代码执行** | 直接传递闭包/回调函数 | 必须执行系统命令 |
| **变量访问** | 直接访问父进程变量 | 需要序列化传递 |
| **对象传递** | 直接引用对象 | 需要重新创建对象 |
| **文件依赖** | 无需创建额外文件 | 需要脚本文件 |
| **调试便利** | 代码在同一文件中 | 需要查看生成的脚本 |
| **性能开销** | 低（内存操作） | 中等（文件I/O + 进程创建） |
| **平台支持** | 主要Linux/macOS | 完全跨平台 |
| **安装要求** | 需要Swoole扩展 | 仅需标准PHP |

## 4. 优化方案对比

### 方案1：永久工作脚本（推荐）

```php
// 优势：只创建一次脚本文件
private static ?string $workerScriptPath = null;

private function ensureWorkerScript(): void
{
    if (self::$workerScriptPath === null) {
        self::$workerScriptPath = $this->createPermanentWorkerScript();
    }
}
```

**优势**：
- ✅ 减少文件I/O操作
- ✅ 便于调试和维护
- ✅ 更好的性能
- ✅ 可以版本控制

**劣势**：
- ❌ 仍需要文件系统操作
- ❌ 配置变更需要重新生成

### 方案2：PHP内置服务器方案

```php
// 实验性方案：使用 -r 参数
$inlineCode = sprintf(
    'require_once "%s"; $task = unserialize(base64_decode("%s")); executeTask($task);',
    $autoloaderPath,
    base64_encode(serialize($taskData))
);

$process = new Process(['php', '-r', $inlineCode]);
```

**优势**：
- ✅ 无需创建文件
- ✅ 动态执行

**劣势**：
- ❌ 命令行长度限制
- ❌ 序列化开销
- ❌ 调试困难

### 方案3：标准输入传递

```php
$process = new Process(['php', 'worker.php', '--stdin']);
$process->setInput(json_encode($taskData));
```

**优势**：
- ✅ 避免命令行长度限制
- ✅ 数据传递灵活

**劣势**：
- ❌ 仍需要工作脚本
- ❌ 增加复杂性

## 5. 最佳实践建议

### 推荐的实现策略

1. **生产环境**：使用永久工作脚本
```php
// 在项目中创建固定的工作脚本
// examples/workers/recording_worker.php
```

2. **开发环境**：使用调试友好的方案
```php
// 生成可读性好的脚本，便于调试
$scriptContent = $this->generateDebugFriendlyScript();
```

3. **配置管理**：
```php
// 使用环境变量或配置文件，避免硬编码
$workerScript = $_ENV['WORKER_SCRIPT_PATH'] ?? __DIR__ . '/workers/recording_worker.php';
```

### 性能优化建议

```php
// 1. 脚本缓存
private static array $scriptCache = [];

// 2. 进程复用
$this->processPool->setReuseProcesses(true);

// 3. 批量处理
$this->processBatch($urls, $batchSize = 10);
```

### 错误处理最佳实践

```php
// 1. 完善的错误日志
fwrite(STDERR, "[Worker] Error: " . $e->getMessage() . "\n");

// 2. 退出码标准化
exit($e instanceof RecoverableException ? 2 : 1);

// 3. 超时处理
$process->setTimeout($this->config['process']['timeout']);
```

## 6. 总结

### 技术选择建议

1. **如果可以使用 Swoole**：
   - 高并发场景（1000+ 并发）
   - 性能要求极高
   - 团队熟悉 Swoole

2. **如果必须使用 Symfony Process**：
   - 跨平台兼容性要求
   - 无法安装 Swoole 扩展
   - 稳定性优先于性能

### 实现方案选择

1. **小规模应用**（< 10 并发）：
   - 使用临时脚本方案，简单直接

2. **中等规模应用**（10-100 并发）：
   - 使用永久工作脚本，平衡性能和复杂性

3. **大规模应用**（> 100 并发）：
   - 考虑使用 Swoole 或其他高性能方案

### 关键要点

- Symfony Process 的限制是架构性的，无法完全避免
- 永久工作脚本是当前最佳的折中方案
- 在选择技术方案时，需要权衡性能、复杂性和维护成本
- 对于大多数应用场景，Symfony Process 方案已经足够
