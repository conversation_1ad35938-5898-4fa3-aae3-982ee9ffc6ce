<?php

declare(strict_types=1);

require_once __DIR__ . '/../vendor/autoload.php';

use LiveStream\Recording\PendingRecorder;
use LiveStream\Recording\RecordrConnector;
use LiveStream\Platforms\Douyin\Live;
use LiveStream\Enum\Quality;
use LiveStream\Enum\OutputFormat;
use LiveStream\Config\RecordingOptions;

echo "=== 流URL管理功能演示 ===\n\n";

// 模拟完整的流URL数据
$completeStreamUrl = [
    'flv_pull_url' => [
        'ORIGIN' => 'https://example.com/origin.flv',
        'FULL_HD1' => 'https://example.com/fullhd.flv',
        'HD1' => 'https://example.com/hd.flv',
        'SD1' => 'https://example.com/sd.flv',
        'SD2' => 'https://example.com/smooth.flv',
    ],
    'hls_pull_url_map' => [
        'ORIGIN' => 'https://example.com/origin.m3u8',
        'FULL_HD1' => 'https://example.com/fullhd.m3u8',
        'HD1' => 'https://example.com/hd.m3u8',
        'SD1' => 'https://example.com/sd.m3u8',
        'SD2' => 'https://example.com/smooth.m3u8',
    ],
];

// 模拟部分清晰度缺失的流URL数据
$partialStreamUrl = [
    'flv_pull_url' => [
        'HD1' => 'https://example.com/hd.flv',
        'SD1' => 'https://example.com/sd.flv',
        'SD2' => 'https://example.com/smooth.flv',
    ],
    'hls_pull_url_map' => [
        'HD1' => 'https://example.com/hd.m3u8',
        'SD1' => 'https://example.com/sd.m3u8',
        'SD2' => 'https://example.com/smooth.m3u8',
    ],
];

// 创建 Live 对象
$completeLive = new Live(2, 'Complete Stream', 'Test Anchor', '123', $completeStreamUrl, '1000');
$partialLive = new Live(2, 'Partial Stream', 'Test Anchor', '456', $partialStreamUrl, '500');

// 创建 RecordrConnector 和配置
$connector = new RecordrConnector();
$config = new RecordingOptions([
    'quality' => null, // 使用智能降级
    'save_path' => '/tmp',
    'format' => OutputFormat::MP4,
]);
$connector->withConfig($config);

// 创建 mock 平台接口
$mockPlatform = new class($completeLive) {
    private $live;
    
    public function __construct($live) {
        $this->live = $live;
    }
    
    public function getLive() {
        return $this->live;
    }
    
    public function getReferer() {
        return 'https://example.com';
    }
    
    public function getPlatformName() {
        return 'demo';
    }
    
    public function supportsUrl($url) {
        return true;
    }
};

echo "1. 完整清晰度可用时的流URL管理：\n";
$pendingRecorder1 = new PendingRecorder($connector, $mockPlatform);

echo "   获取HLS URL (智能降级): " . ($pendingRecorder1->getValidatedStreamUrl('hls') ?? 'null') . "\n";
echo "   获取FLV URL (智能降级): " . ($pendingRecorder1->getValidatedStreamUrl('flv') ?? 'null') . "\n";
echo "   选择的清晰度: " . ($pendingRecorder1->getSelectedQuality()?->value ?? 'null') . "\n";
echo "   期望：返回最高清晰度 (ORIGIN)\n\n";

echo "2. 部分清晰度缺失时的智能降级：\n";
$mockPlatform2 = new class($partialLive) {
    private $live;
    
    public function __construct($live) {
        $this->live = $live;
    }
    
    public function getLive() {
        return $this->live;
    }
    
    public function getReferer() {
        return 'https://example.com';
    }
    
    public function getPlatformName() {
        return 'demo';
    }
    
    public function supportsUrl($url) {
        return true;
    }
};

$pendingRecorder2 = new PendingRecorder($connector, $mockPlatform2);

echo "   获取HLS URL (智能降级): " . ($pendingRecorder2->getValidatedStreamUrl('hls') ?? 'null') . "\n";
echo "   获取FLV URL (智能降级): " . ($pendingRecorder2->getValidatedStreamUrl('flv') ?? 'null') . "\n";
echo "   选择的清晰度: " . ($pendingRecorder2->getSelectedQuality()?->value ?? 'null') . "\n";
echo "   期望：自动降级到 HD1 (高清)\n\n";

echo "3. 流URL缓存机制：\n";
echo "   第一次获取HLS URL: " . ($pendingRecorder1->getValidatedStreamUrl('hls') ?? 'null') . "\n";
echo "   第二次获取HLS URL (从缓存): " . ($pendingRecorder1->getValidatedStreamUrl('hls') ?? 'null') . "\n";
echo "   期望：两次返回相同的URL，第二次从缓存获取\n\n";

echo "4. 流URL刷新功能：\n";
echo "   刷新前的HLS URL: " . ($pendingRecorder1->getValidatedStreamUrl('hls') ?? 'null') . "\n";
$pendingRecorder1->refreshStreamUrls();
echo "   刷新后的HLS URL: " . ($pendingRecorder1->getValidatedStreamUrl('hls') ?? 'null') . "\n";
echo "   期望：刷新后重新获取流URL\n\n";

echo "5. 流验证功能：\n";
$testUrl = 'https://example.com/test.m3u8';
echo "   验证URL连接: " . ($pendingRecorder1->validateStreamConnection($testUrl) ? 'true' : 'false') . "\n";
echo "   验证HLS格式: " . ($pendingRecorder1->validateStreamFormat($testUrl, 'hls') ? 'true' : 'false') . "\n";
echo "   验证FLV格式: " . ($pendingRecorder1->validateStreamFormat($testUrl, 'flv') ? 'true' : 'false') . "\n";
echo "   期望：连接验证可能失败（网络原因），格式验证HLS通过，FLV失败\n\n";

echo "6. 错误处理：\n";
try {
    $pendingRecorder1->getValidatedStreamUrl('invalid');
} catch (InvalidArgumentException $e) {
    echo "   无效流类型错误: " . $e->getMessage() . "\n";
}
echo "   期望：抛出InvalidArgumentException\n\n";

echo "7. 向后兼容性：\n";
echo "   传统getLive()方法: " . get_class($pendingRecorder1->getLive()) . "\n";
echo "   传统savePath()方法: " . (strlen($pendingRecorder1->savePath()) > 0 ? '正常工作' : '失败') . "\n";
echo "   期望：现有方法继续正常工作\n\n";

echo "=== 演示完成 ===\n";
echo "\n总结：\n";
echo "✅ 智能降级功能：自动从最高清晰度降级到可用清晰度\n";
echo "✅ 流URL缓存：避免重复获取，提高性能\n";
echo "✅ 流验证功能：验证URL连接和格式\n";
echo "✅ 错误处理：优雅处理各种异常情况\n";
echo "✅ 向后兼容：不影响现有代码的使用方式\n";
