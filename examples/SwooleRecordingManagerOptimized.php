<?php

declare(strict_types=1);

namespace Examples;

require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/config/SwooleRecordingConfig.php';

use Examples\Config\SwooleRecordingConfig;
use LiveStream\PlatformFactory;
use LiveStream\Platforms\PlatformManager;
use LiveStream\Config\RecordingOptions;
use LiveStream\Recording\RecordrConnector;
use LiveStream\Recording\Pipes\DebugInfoPipe;
use LiveStream\Recording\Drivers\NativeFFmpegRecorder;
use LiveStream\Recording\ProcessRunner;
use LiveStream\Exceptions\StreamNotLiveException;
use LiveStream\Exceptions\StreamUnavailableException;
use Swoole\Coroutine;
use Swoole\Coroutine\WaitGroup;

/**
 * 优化版 Swoole 协程录制管理器
 * 
 * 主要优化：
 * 1. 强制使用 NativeFFmpegRecorder 避免阻塞
 * 2. 智能检测录制器类型
 * 3. 根据录制器类型选择处理策略
 * 4. 优化并发控制和资源管理
 */
class SwooleRecordingManagerOptimized
{
    private array $config;
    private PlatformManager $platformManager;
    private array $statistics = [
        'total_tasks' => 0,
        'completed_tasks' => 0,
        'failed_tasks' => 0,
        'start_time' => 0,
        'memory_peak' => 0,
    ];
    private bool $isUsingBlockingRecorder = false;

    public function __construct(array $config = [])
    {
        $this->config = SwooleRecordingConfig::mergeConfig($config);
        SwooleRecordingConfig::validateConfig($this->config);
        
        // 创建共享的平台管理器
        $this->platformManager = new PlatformManager(new PlatformFactory());
        
        $this->statistics['start_time'] = microtime(true);
        
        // 检测和优化录制器配置
        $this->optimizeRecorderConfig();
        
        $this->log('info', 'SwooleRecordingManagerOptimized 初始化完成', [
            'max_coroutines' => $this->config['swoole']['max_coroutines'],
            'total_urls' => count($this->config['urls']),
            'recorder_type' => $this->config['recording']['recorder_type'] ?? 'auto',
            'is_blocking_recorder' => $this->isUsingBlockingRecorder,
        ]);
    }

    /**
     * 优化录制器配置
     */
    private function optimizeRecorderConfig(): void
    {
        // 检查是否强制指定了录制器类型
        $recorderType = $this->config['recording']['recorder_type'] ?? 'auto';
        
        if ($recorderType === 'php-ffmpeg') {
            $this->isUsingBlockingRecorder = true;
            $this->log('warning', '检测到 PhpFFMpegRecorder，这会阻塞协程执行', [
                'recommendation' => '建议使用 NativeFFmpegRecorder 获得更好的异步性能'
            ]);
            
            // 调整并发数量以适应阻塞模式
            if ($this->config['swoole']['max_coroutines'] > 10) {
                $this->config['swoole']['max_coroutines'] = 5;
                $this->log('info', '阻塞模式下自动调整并发数量', [
                    'adjusted_max_coroutines' => 5
                ]);
            }
        } else {
            // 默认或强制使用异步录制器
            $this->config['recording']['recorder_type'] = 'native';
            $this->isUsingBlockingRecorder = false;
            $this->log('info', '使用异步 NativeFFmpegRecorder', [
                'max_coroutines' => $this->config['swoole']['max_coroutines']
            ]);
        }
    }

    /**
     * 开始录制任务
     */
    public function startRecording(): void
    {
        $urls = $this->config['urls'];
        $this->statistics['total_tasks'] = count($urls);
        
        $this->log('info', '开始录制任务', [
            'total_urls' => count($urls),
            'max_coroutines' => $this->config['swoole']['max_coroutines'],
            'processing_mode' => $this->isUsingBlockingRecorder ? 'blocking' : 'async',
        ]);

        // 启动监控协程
        if ($this->config['swoole']['enable_monitoring']) {
            $this->startMonitoring();
        }

        if ($this->isUsingBlockingRecorder) {
            // 阻塞模式：使用串行或小批次处理
            $this->processUrlsInBlockingMode($urls);
        } else {
            // 异步模式：使用批次并发处理
            $this->processUrlsInAsyncMode($urls);
        }
        
        $this->logFinalStatistics();
    }

    /**
     * 阻塞模式处理URL
     */
    private function processUrlsInBlockingMode(array $urls): void
    {
        $this->log('info', '使用阻塞模式处理URL', [
            'strategy' => 'serial_processing',
            'reason' => 'PhpFFMpegRecorder 会阻塞协程'
        ]);

        // 串行处理，避免创建无效的协程
        foreach ($urls as $index => $url) {
            $urlIndex = $index + 1;
            $this->log('info', "串行处理URL {$urlIndex}/{$this->statistics['total_tasks']}", [
                'url' => $this->maskUrl($url),
                'progress' => round($urlIndex / $this->statistics['total_tasks'] * 100, 1) . '%'
            ]);
            
            $this->processUrl($url, $urlIndex);
        }
    }

    /**
     * 异步模式处理URL
     */
    private function processUrlsInAsyncMode(array $urls): void
    {
        $maxCoroutines = $this->config['swoole']['max_coroutines'];
        $batches = array_chunk($urls, $maxCoroutines);
        
        $this->log('info', '使用异步模式分批处理URL', [
            'total_batches' => count($batches),
            'batch_size' => $maxCoroutines,
            'strategy' => 'concurrent_batches'
        ]);

        // 逐批处理
        foreach ($batches as $batchIndex => $batch) {
            $this->processBatch($batch, $batchIndex + 1);
        }
    }

    /**
     * 处理一批URL（异步模式）
     */
    private function processBatch(array $urls, int $batchIndex): void
    {
        $this->log('info', "开始处理批次", [
            'batch_index' => $batchIndex,
            'batch_size' => count($urls),
        ]);

        // 创建 WaitGroup 来等待当前批次的所有协程完成
        $waitGroup = new WaitGroup();

        // 为当前批次的每个 URL 创建协程
        foreach ($urls as $index => $url) {
            $waitGroup->add();
            $urlIndex = ($batchIndex - 1) * $this->config['swoole']['max_coroutines'] + $index + 1;
            $this->createUrlCoroutine($url, $urlIndex, $waitGroup);
        }

        // 等待当前批次的所有协程完成
        $waitGroup->wait();

        $this->log('info', "批次处理完成", [
            'batch_index' => $batchIndex,
            'batch_size' => count($urls),
        ]);
    }

    /**
     * 为单个 URL 创建协程
     */
    private function createUrlCoroutine(string $url, int $urlIndex, WaitGroup $waitGroup): void
    {
        Coroutine::create(function () use ($url, $urlIndex, $waitGroup) {
            try {
                $this->log('info', "URL协程启动", [
                    'url' => $this->maskUrl($url),
                    'url_index' => $urlIndex,
                    'coroutine_id' => Coroutine::getCid(),
                ]);

                // 处理URL
                $this->processUrl($url, $urlIndex);

            } catch (\Throwable $e) {
                $this->log('error', "URL协程异常", [
                    'url' => $this->maskUrl($url),
                    'url_index' => $urlIndex,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                ]);
            } finally {
                // 标记协程完成
                $waitGroup->done();
                
                $this->log('info', "URL协程结束", [
                    'url' => $this->maskUrl($url),
                    'url_index' => $urlIndex,
                ]);
            }
        });
    }

    /**
     * 处理单个URL
     */
    private function processUrl(string $url, int $urlIndex): void
    {
        $startTime = microtime(true);
        $startMemory = memory_get_usage();
        
        $this->log('info', "开始处理URL", [
            'url' => $this->maskUrl($url),
            'url_index' => $urlIndex,
            'coroutine_id' => Coroutine::getCid(),
        ]);

        try {
            // 获取平台实例
            $platform = $this->platformManager->driver($url);
            
            // 创建录制配置
            $options = $this->createRecordingOptions();
            
            // 创建录制连接器
            $recordrConnector = $this->createOptimizedRecordrConnector($options);
            
            // 执行录制
            $recordrConnector->handle($platform, function (string $type, string $buffer) use ($url, $urlIndex) {
                $this->log('debug', "FFmpeg输出", [
                    'url' => $this->maskUrl($url),
                    'url_index' => $urlIndex,
                    'type' => $type,
                    'buffer' => trim($buffer),
                ]);
            });

            $this->statistics['completed_tasks']++;
            
            $endTime = microtime(true);
            $endMemory = memory_get_usage();
            
            $this->log('info', "URL处理完成", [
                'url' => $this->maskUrl($url),
                'url_index' => $urlIndex,
                'duration' => round($endTime - $startTime, 2),
                'memory_used' => round(($endMemory - $startMemory) / 1024 / 1024, 2) . 'MB',
            ]);

        } catch (\Throwable $e) {
            $this->statistics['failed_tasks']++;
            
            $this->log('error', "URL处理失败", [
                'url' => $this->maskUrl($url),
                'url_index' => $urlIndex,
                'error' => $e->getMessage(),
                'exception_type' => get_class($e),
            ]);
        }
    }

    /**
     * 创建优化的录制连接器
     */
    private function createOptimizedRecordrConnector(RecordingOptions $options): RecordrConnector
    {
        $recordrConnector = new RecordrConnector();
        
        $recordrConnector->withConfig($options);
        
        // 根据配置选择录制器
        if ($this->config['recording']['recorder_type'] === 'native') {
            // 强制使用异步的 NativeFFmpegRecorder
            $recordrConnector->withRecordr(new NativeFFmpegRecorder(
                runner: new ProcessRunner(),
                ffmpegBinary: 'ffmpeg'
            ));
            
            $this->log('debug', '使用 NativeFFmpegRecorder（异步模式）');
        }
        // 如果是 php-ffmpeg，使用默认配置（会阻塞）
        
        // 使用专业的调试中间件
        if ($this->config['swoole']['enable_debug']) {
            $recordrConnector->middleware()->pipe(new DebugInfoPipe());
        }
        
        $recordrConnector
            ->withTries($this->config['recording']['max_retries'])
            ->withRetryInterval($this->config['recording']['retry_interval'])
            ->withExponentialBackoff($this->config['recording']['exponential_backoff']);

        // 设置重试逻辑
        $recordrConnector->withShouldRetry(function (\Throwable $exception, int $attempt) {
            return $this->shouldRetry($exception, $attempt);
        });

        return $recordrConnector;
    }

    // ... 其他方法保持不变（createRecordingOptions, shouldRetry, startMonitoring 等）
    
    private function createRecordingOptions(): RecordingOptions
    {
        $options = new RecordingOptions();
        $recordingConfig = $this->config['recording'];
        
        $options->setSavePath($recordingConfig['save_path']);
        $options->setFormat($recordingConfig['format']);
        
        $options->set([
            'timeout' => $recordingConfig['timeout'],
            'max_retries' => $recordingConfig['max_retries'],
            'custom_headers' => $recordingConfig['custom_headers'],
        ]);

        return $options;
    }

    private function shouldRetry(\Throwable $exception, int $attempt): bool
    {
        $exceptionClass = get_class($exception);
        
        // 不重试的异常类型
        if (in_array($exceptionClass, $this->config['no_retry_exceptions'])) {
            return false;
        }
        
        // 重试的异常类型
        if (in_array($exceptionClass, $this->config['retry_exceptions'])) {
            return true;
        }
        
        return false;
    }

    private function log(string $level, string $message, array $context = []): void
    {
        if (!$this->config['logging']['enable']) {
            return;
        }

        $timestamp = date('Y-m-d H:i:s');
        $contextStr = '';
        
        if (!empty($context)) {
            if ($this->config['logging']['include_memory_usage']) {
                $context['memory'] = round(memory_get_usage() / 1024 / 1024, 2) . 'MB';
            }
            
            if ($this->config['logging']['include_coroutine_id']) {
                $context['cid'] = Coroutine::getCid();
            }
            
            $contextStr = ' ' . json_encode($context, JSON_UNESCAPED_UNICODE);
        }

        printf(
            $this->config['logging']['format'] . "\n",
            $timestamp,
            strtoupper($level),
            $message . $contextStr
        );
    }

    private function maskUrl(string $url): string
    {
        $parsed = parse_url($url);
        $host = $parsed['host'] ?? 'unknown';
        $path = $parsed['path'] ?? '';
        
        return $host . $path . '?...';
    }

    private function startMonitoring(): void
    {
        // 监控实现...
    }

    private function logFinalStatistics(): void
    {
        $runtime = microtime(true) - $this->statistics['start_time'];
        
        $this->log('info', '录制任务完成', [
            'total_runtime' => round($runtime, 2) . 's',
            'completed_tasks' => $this->statistics['completed_tasks'],
            'failed_tasks' => $this->statistics['failed_tasks'],
            'total_tasks' => $this->statistics['total_tasks'],
            'success_rate' => round($this->statistics['completed_tasks'] / $this->statistics['total_tasks'] * 100, 2) . '%',
            'processing_mode' => $this->isUsingBlockingRecorder ? 'blocking' : 'async',
        ]);
    }
}
