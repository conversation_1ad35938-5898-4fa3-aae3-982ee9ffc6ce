# ProcessRecordingManager vs SwooleRecordingManager

## 概述

本文档详细对比了使用 Symfony Process 组件的 `ProcessRecordingManager` 与使用 Swoole 协程的 `SwooleRecordingManager` 之间的差异和优势。

## 核心差异对比

### 1. 并发模型

| 特性 | SwooleRecordingManager | ProcessRecordingManager |
|------|----------------------|------------------------|
| 并发模型 | Swoole 协程 | Symfony Process 进程 |
| 并发控制 | `max_coroutines` | `max_processes` |
| 资源隔离 | 协程级别（共享内存） | 进程级别（完全隔离） |
| 错误影响 | 可能影响其他协程 | 完全隔离，不影响其他进程 |

### 2. 配置对比

#### Swoole 配置
```php
'swoole' => [
    'max_coroutines' => 10000,
    'enable_debug' => true,
    'memory_limit' => 100 * 1024 * 1024,
    'gc_interval' => 60,
    'enable_monitoring' => true,
],
'logging' => [
    'include_coroutine_id' => true,
]
```

#### Process 配置
```php
'process' => [
    'max_processes' => 10,
    'process_timeout' => 3600,
    'poll_interval' => 100000,
    'enable_debug' => true,
    'memory_limit' => 100 * 1024 * 1024,
    'gc_interval' => 60,
    'enable_monitoring' => true,
    'monitoring_interval' => 5,
],
'logging' => [
    'include_process_id' => true,
]
```

### 3. 功能对比

| 功能 | SwooleRecordingManager | ProcessRecordingManager |
|------|----------------------|------------------------|
| 批次处理 | ✅ 支持 | ✅ 支持 |
| 错误重试 | ✅ 支持 | ✅ 支持 |
| 监控统计 | ✅ 协程监控 | ✅ 进程监控 |
| 内存管理 | ✅ 协程级GC | ✅ 进程级GC |
| 日志记录 | ✅ 协程ID | ✅ 进程ID |
| 优雅停止 | ✅ 协程停止 | ✅ 进程终止 |

## 优势对比

### ProcessRecordingManager 优势

1. **更好的稳定性**
   - 进程间完全隔离，单个录制任务崩溃不影响其他任务
   - 不依赖 Swoole 扩展，减少环境依赖

2. **更强的兼容性**
   - 基于标准 PHP 和 Symfony Process，兼容性更好
   - 可在任何支持 PHP 的环境中运行

3. **更简单的部署**
   - 无需安装 Swoole 扩展
   - 标准 PHP 环境即可运行

4. **更好的资源管理**
   - 进程级别的内存隔离
   - 自动清理临时文件和进程

### SwooleRecordingManager 优势

1. **更高的性能**
   - 协程切换开销小于进程创建开销
   - 内存共享，资源利用率更高

2. **更大的并发数**
   - 可支持数千个并发协程
   - 内存占用相对较小

## 使用场景建议

### 推荐使用 ProcessRecordingManager 的场景

1. **生产环境部署**
   - 需要最大稳定性
   - 无法安装 Swoole 扩展
   - 录制任务可能不稳定

2. **开发和测试**
   - 简化环境配置
   - 便于调试和问题定位

3. **中小规模录制**
   - 并发数在 100 以内
   - 对性能要求不是特别高

### 推荐使用 SwooleRecordingManager 的场景

1. **高并发场景**
   - 需要同时录制数百个流
   - 对性能要求极高

2. **资源受限环境**
   - 内存或CPU资源有限
   - 需要最大化资源利用率

## 迁移指南

### 从 Swoole 迁移到 Process

1. **配置转换**
```php
// 自动转换 Swoole 配置
$swooleConfig = [...]; // 原有配置
$processConfig = ProcessRecordingConfig::convertFromSwooleConfig($swooleConfig);
$manager = new ProcessRecordingManager($processConfig);
```

2. **代码替换**
```php
// 原代码
$manager = new SwooleRecordingManager($config);

// 新代码
$manager = new ProcessRecordingManager($config);
```

3. **配置调整**
   - 将 `max_coroutines` 调整为合理的 `max_processes`（建议 10-50）
   - 添加 `process_timeout` 配置
   - 调整 `monitoring_interval` 为合适的值

### 配置建议

```php
$config = [
    'process' => [
        'max_processes' => 10,        // 根据服务器性能调整
        'process_timeout' => 3600,    // 1小时超时
        'poll_interval' => 100000,    // 100ms轮询间隔
        'monitoring_interval' => 5,   // 5秒监控间隔
    ],
];
```

## 性能对比

| 指标 | SwooleRecordingManager | ProcessRecordingManager |
|------|----------------------|------------------------|
| 启动时间 | 快 | 中等 |
| 内存占用 | 低 | 中等 |
| CPU 占用 | 低 | 中等 |
| 最大并发 | 高（1000+） | 中等（100以内） |
| 稳定性 | 中等 | 高 |
| 故障隔离 | 弱 | 强 |

## 总结

`ProcessRecordingManager` 提供了一个稳定、兼容性强的替代方案，特别适合生产环境和中小规模的录制任务。虽然在极高并发场景下性能不如 Swoole 版本，但在大多数实际应用场景中，其稳定性和易用性优势更为重要。

选择哪个版本主要取决于：
- **稳定性要求**：ProcessRecordingManager 更稳定
- **并发需求**：SwooleRecordingManager 支持更高并发
- **环境限制**：ProcessRecordingManager 环境要求更低
- **维护成本**：ProcessRecordingManager 更易维护
