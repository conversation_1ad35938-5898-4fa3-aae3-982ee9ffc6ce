<?php

declare(strict_types=1);

require_once __DIR__ . '/../vendor/autoload.php';

use LiveStream\Platforms\Douyin\Live;
use LiveStream\Stream\Stream;
use LiveStream\Enum\Quality;
use LiveStream\Enum\StreamType;
use Carbon\Carbon;

echo "=== 直播流过期功能演示 ===\n\n";

// 1. 演示 Stream 类的 expire 功能
echo "1. Stream 类的 expire 功能演示:\n";
echo "--------------------------------\n";

// 创建一个带有过期时间的流
$expireTime = Carbon::now()->addHours(2);
$stream = Stream::createFlv(
    'https://example.com/stream.flv',
    Quality::HD1,
    false,
    $expireTime
);

echo "流URL: " . $stream->getUrl() . "\n";
echo "流类型: " . $stream->getType() . "\n";
echo "清晰度: " . $stream->getQuality()->value . "\n";
echo "过期时间: " . $stream->getExpire()->toDateTimeString() . "\n";
echo "是否已过期: " . ($stream->isExpired() ? '是' : '否') . "\n";
echo "距离过期时间: " . $stream->getTimeToExpire() . " 秒\n";
echo "\n";

// 2. 演示 Douyin Live 类的 expire 解析功能
echo "2. Douyin Live 类的 expire 解析功能演示:\n";
echo "----------------------------------------\n";

// 模拟带有 expire 参数的抖音直播流 URL
$futureTimestamp = Carbon::now()->addHours(3)->timestamp;
$streamUrls = [
    StreamType::FLV->value => [
        Quality::ORIGIN->value => "http://pull-flv-l11.douyincdn.com/third/stream-694230885870862812_or4.flv?arch_hrchy=w1&exp_hrchy=w1&expire={$futureTimestamp}&major_anchor_level=common&sign=9eec3fe889997888c49871d9339148f4&t_id=037-2025081120254510A48334857E66CDC1B9-9zL49X&unique_id=stream-694230885870862812_476_flv_or4&codec=h264",
        Quality::HD1->value => "http://pull-flv-l11.douyincdn.com/third/stream-694230885870862812_hd.flv?arch_hrchy=w1&exp_hrchy=w1&expire={$futureTimestamp}&major_anchor_level=common&sign=9f0043fb6a9ed9fade3fd4bcdadf9ad0&t_id=037-2025081120254510A48334857E66CDC1B9-9zL49X&unique_id=stream-694230885870862812_476_flv_hd",
    ],
    StreamType::HLS->value => [
        Quality::ORIGIN->value => "http://pull-hls-l11.douyincdn.com/third/stream-694230885870862812_or4.m3u8?arch_hrchy=w1&exp_hrchy=w1&expire={$futureTimestamp}&major_anchor_level=common&sign=63015841b626f417b3737b61ec815530&t_id=037-2025081120254510A48334857E66CDC1B9-9zL49X&codec=h264",
        Quality::HD1->value => "http://pull-hls-l11.douyincdn.com/third/stream-694230885870862812_hd.m3u8?arch_hrchy=w1&exp_hrchy=w1&expire={$futureTimestamp}&major_anchor_level=common&sign=e4d2d1010515a2da16869b9735d4118a&t_id=037-2025081120254510A48334857E66CDC1B9-9zL49X",
    ],
];

$live = new Live(
    2, // 直播状态
    '2025到底应该怎么办！？', // 直播标题
    '急速引擎', // 主播名称
    '7537288513609927476', // 房间ID
    $streamUrls,
    '77' // 观看人数
);

// 获取 FLV 流并显示过期信息
$flvStream = $live->getFlvUrl(Quality::ORIGIN);
if ($flvStream) {
    echo "FLV 流信息:\n";
    echo "  URL: " . substr($flvStream->getUrl(), 0, 80) . "...\n";
    echo "  清晰度: " . $flvStream->getQuality()->value . "\n";
    echo "  过期时间: " . $flvStream->getExpire()->toDateTimeString() . "\n";
    echo "  是否已过期: " . ($flvStream->isExpired() ? '是' : '否') . "\n";
    echo "  距离过期: " . $flvStream->getTimeToExpire() . " 秒\n";
    echo "\n";
}

// 获取 HLS 流并显示过期信息
$hlsStream = $live->getHlsUrl(Quality::HD1);
if ($hlsStream) {
    echo "HLS 流信息:\n";
    echo "  URL: " . substr($hlsStream->getUrl(), 0, 80) . "...\n";
    echo "  清晰度: " . $hlsStream->getQuality()->value . "\n";
    echo "  过期时间: " . $hlsStream->getExpire()->toDateTimeString() . "\n";
    echo "  是否已过期: " . ($hlsStream->isExpired() ? '是' : '否') . "\n";
    echo "  距离过期: " . $hlsStream->getTimeToExpire() . " 秒\n";
    echo "\n";
}

// 3. 演示智能降级功能（自动选择最佳清晰度）
echo "3. 智能降级功能演示:\n";
echo "--------------------\n";

$autoFlvStream = $live->getFlvUrl(null); // 不指定清晰度，使用智能降级
if ($autoFlvStream) {
    echo "自动选择的 FLV 流:\n";
    echo "  清晰度: " . $autoFlvStream->getQuality()->value . "\n";
    echo "  过期时间: " . $autoFlvStream->getExpire()->toDateTimeString() . "\n";
    echo "\n";
}

// 4. 演示流的元数据信息
echo "4. 流的元数据信息:\n";
echo "------------------\n";

$metadata = $flvStream->getMetadata();
echo "完整的元数据信息:\n";
echo "  URL: " . substr($metadata['url'], 0, 50) . "...\n";
echo "  类型: " . $metadata['type'] . "\n";
echo "  清晰度: " . $metadata['quality']['display_name'] . "\n";
echo "  是否已验证: " . ($metadata['validated'] ? '是' : '否') . "\n";
echo "  过期信息:\n";
echo "    时间戳: " . $metadata['expire']['timestamp'] . "\n";
echo "    ISO格式: " . $metadata['expire']['iso_string'] . "\n";
echo "    可读格式: " . $metadata['expire']['human_readable'] . "\n";
echo "    是否已过期: " . ($metadata['expire']['is_expired'] ? '是' : '否') . "\n";
echo "    距离过期: " . $metadata['expire']['time_to_expire'] . " 秒\n";
echo "\n";

// 5. 演示数组转换功能
echo "5. 数组转换功能演示:\n";
echo "--------------------\n";

$streamArray = $flvStream->toArray();
echo "流转换为数组:\n";
echo "  URL: " . substr($streamArray['url'], 0, 50) . "...\n";
echo "  类型: " . $streamArray['type'] . "\n";
echo "  过期时间戳: " . $streamArray['expire_timestamp'] . "\n";
echo "  过期ISO格式: " . substr($streamArray['expire'], 0, 25) . "...\n";
echo "\n";

echo "=== 演示完成 ===\n";
