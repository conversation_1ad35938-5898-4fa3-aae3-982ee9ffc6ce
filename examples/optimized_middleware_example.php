<?php

declare(strict_types=1);

require_once __DIR__ . '/../vendor/autoload.php';

use LiveStream\Pipeline;

echo "=== 中间件合并 vs 分离对比演示 ===\n\n";

// 模拟数据结构
class MockLive
{
    public function __construct(private bool $isLive, private array $streamUrls = [])
    {
    }
    
    public function isLive(): bool
    {
        return $this->isLive;
    }
    
    public function getAnchorName(): string
    {
        return '测试主播';
    }
    
    public function findAvailableUrl($type): ?MockStream
    {
        if (empty($this->streamUrls)) {
            return null;
        }
        return new MockStream('http://example.com/stream.m3u8');
    }
}

class MockStream
{
    public function __construct(private string $url)
    {
    }
    
    public function getUrl(): string
    {
        return $this->url;
    }
    
    public function validateUrl(): bool
    {
        return filter_var($this->url, FILTER_VALIDATE_URL) !== false;
    }
    
    public function validateFormat(): bool
    {
        return str_ends_with($this->url, '.m3u8') || str_ends_with($this->url, '.flv');
    }
    
    public function validateConnection(): bool
    {
        // 模拟网络检查
        return true;
    }
    
    public function isExpired(): bool
    {
        return false;
    }
}

class MockPendingRecorder
{
    private ?MockStream $stream = null;
    
    public function __construct(private MockLive $live)
    {
    }
    
    public function getLive(): MockLive
    {
        return $this->live;
    }
    
    public function getStream(): ?MockStream
    {
        return $this->stream;
    }
    
    public function withStream(MockStream $stream): void
    {
        $this->stream = $stream;
    }
}

// 演示1：当前分离的中间件模式
echo "📋 演示1：当前分离的中间件模式\n";
echo "优点：职责清晰，易于测试和维护\n\n";

class CurrentValidateOptionsPipe
{
    public function handle($pendingRecorder, \Closure $next)
    {
        echo "🔍 [ValidateOptionsPipe] 检查直播状态\n";
        
        if (!$pendingRecorder->getLive()->isLive()) {
            throw new Exception("主播未在直播");
        }
        
        echo "✅ [ValidateOptionsPipe] 直播状态验证通过\n";
        return $next($pendingRecorder);
    }
}

class CurrentStreamValidationPipe
{
    public function handle($pendingRecorder, \Closure $next)
    {
        echo "🌊 [StreamValidationPipe] 开始流验证\n";
        
        // 问题：重复检查直播状态
        if (!$pendingRecorder->getLive()->isLive()) {
            throw new Exception("直播已结束");
        }
        echo "⚠️  [StreamValidationPipe] 重复检查直播状态（冗余）\n";
        
        // 获取流
        $stream = $pendingRecorder->getLive()->findAvailableUrl('hls');
        if (!$stream) {
            throw new Exception("未找到可用流");
        }
        echo "✅ [StreamValidationPipe] 找到可用流\n";
        
        // 验证流
        if (!$stream->validateUrl()) {
            throw new Exception("URL格式无效");
        }
        echo "✅ [StreamValidationPipe] URL格式验证通过\n";
        
        if (!$stream->validateFormat()) {
            throw new Exception("流格式不匹配");
        }
        echo "✅ [StreamValidationPipe] 流格式验证通过\n";
        
        if (!$stream->validateConnection()) {
            throw new Exception("网络连接失败");
        }
        echo "✅ [StreamValidationPipe] 网络连接验证通过\n";
        
        $pendingRecorder->withStream($stream);
        echo "✅ [StreamValidationPipe] 流设置完成\n";
        
        return $next($pendingRecorder);
    }
}

$pipeline1 = new Pipeline();
$pipeline1->pipe(new CurrentValidateOptionsPipe());
$pipeline1->pipe(new CurrentStreamValidationPipe());

$pendingRecorder1 = new MockPendingRecorder(new MockLive(true, ['hls' => 'stream.m3u8']));

try {
    $pipeline1->send($pendingRecorder1)->then(function($pr) {
        echo "🎯 [核心逻辑] 开始录制\n";
        return $pr;
    });
} catch (Exception $e) {
    echo "❌ 错误: " . $e->getMessage() . "\n";
}

echo "\n" . str_repeat("=", 60) . "\n\n";

// 演示2：合并后的中间件（不推荐）
echo "📋 演示2：合并后的中间件模式\n";
echo "缺点：职责混乱，难以测试和维护\n\n";

class MergedValidationPipe
{
    public function handle($pendingRecorder, \Closure $next)
    {
        echo "🔄 [MergedValidationPipe] 开始全面验证\n";
        
        // 1. 检查直播状态
        if (!$pendingRecorder->getLive()->isLive()) {
            throw new Exception("主播未在直播");
        }
        echo "✅ [MergedValidationPipe] 直播状态验证通过\n";
        
        // 2. 获取流
        $stream = $pendingRecorder->getLive()->findAvailableUrl('hls');
        if (!$stream) {
            throw new Exception("未找到可用流");
        }
        echo "✅ [MergedValidationPipe] 找到可用流\n";
        
        // 3. 验证流的各个方面
        if (!$stream->validateUrl()) {
            throw new Exception("URL格式无效");
        }
        echo "✅ [MergedValidationPipe] URL格式验证通过\n";
        
        if (!$stream->validateFormat()) {
            throw new Exception("流格式不匹配");
        }
        echo "✅ [MergedValidationPipe] 流格式验证通过\n";
        
        if (!$stream->validateConnection()) {
            throw new Exception("网络连接失败");
        }
        echo "✅ [MergedValidationPipe] 网络连接验证通过\n";
        
        // 4. 检查过期
        if ($stream->isExpired()) {
            throw new Exception("流已过期");
        }
        echo "✅ [MergedValidationPipe] 过期时间验证通过\n";
        
        // 5. 设置流
        $pendingRecorder->withStream($stream);
        echo "✅ [MergedValidationPipe] 流设置完成\n";
        
        echo "🏁 [MergedValidationPipe] 全面验证完成\n";
        
        return $next($pendingRecorder);
    }
}

$pipeline2 = new Pipeline();
$pipeline2->pipe(new MergedValidationPipe());

$pendingRecorder2 = new MockPendingRecorder(new MockLive(true, ['hls' => 'stream.m3u8']));

try {
    $pipeline2->send($pendingRecorder2)->then(function($pr) {
        echo "🎯 [核心逻辑] 开始录制\n";
        return $pr;
    });
} catch (Exception $e) {
    echo "❌ 错误: " . $e->getMessage() . "\n";
}

echo "\n" . str_repeat("=", 60) . "\n\n";

// 演示3：优化后的分离模式（推荐）
echo "📋 演示3：优化后的分离模式\n";
echo "优点：消除重复，保持职责分离\n\n";

class OptimizedValidateOptionsPipe
{
    public function handle($pendingRecorder, \Closure $next)
    {
        echo "🔍 [OptimizedValidateOptionsPipe] 检查直播状态\n";
        
        if (!$pendingRecorder->getLive()->isLive()) {
            throw new Exception("主播 {$pendingRecorder->getLive()->getAnchorName()} 当前未在直播");
        }
        
        echo "✅ [OptimizedValidateOptionsPipe] 直播状态验证通过\n";
        return $next($pendingRecorder);
    }
}

class OptimizedStreamValidationPipe
{
    public function handle($pendingRecorder, \Closure $next)
    {
        echo "🌊 [OptimizedStreamValidationPipe] 开始流技术验证\n";
        
        // 移除重复的直播状态检查，专注于流的技术验证
        
        // 获取流
        $stream = $pendingRecorder->getLive()->findAvailableUrl('hls');
        if (!$stream) {
            throw new Exception("未找到可用的流URL");
        }
        echo "✅ [OptimizedStreamValidationPipe] 找到可用流\n";
        
        // 技术验证
        $this->validateStreamTechnically($stream);
        $this->validateStreamExpiration($stream);
        
        $pendingRecorder->withStream($stream);
        echo "✅ [OptimizedStreamValidationPipe] 流验证和设置完成\n";
        
        return $next($pendingRecorder);
    }
    
    private function validateStreamTechnically($stream): void
    {
        if (!$stream->validateUrl()) {
            throw new Exception("URL格式无效: " . $stream->getUrl());
        }
        echo "✅ [OptimizedStreamValidationPipe] URL格式验证通过\n";
        
        if (!$stream->validateFormat()) {
            throw new Exception("流格式不匹配: " . $stream->getUrl());
        }
        echo "✅ [OptimizedStreamValidationPipe] 流格式验证通过\n";
        
        if (!$stream->validateConnection()) {
            throw new Exception("网络连接失败: " . $stream->getUrl());
        }
        echo "✅ [OptimizedStreamValidationPipe] 网络连接验证通过\n";
    }
    
    private function validateStreamExpiration($stream): void
    {
        if ($stream->isExpired()) {
            throw new Exception("流已过期");
        }
        echo "✅ [OptimizedStreamValidationPipe] 过期时间验证通过\n";
    }
}

$pipeline3 = new Pipeline();
$pipeline3->pipe(new OptimizedValidateOptionsPipe());
$pipeline3->pipe(new OptimizedStreamValidationPipe());

$pendingRecorder3 = new MockPendingRecorder(new MockLive(true, ['hls' => 'stream.m3u8']));

try {
    $pipeline3->send($pendingRecorder3)->then(function($pr) {
        echo "🎯 [核心逻辑] 开始录制\n";
        return $pr;
    });
} catch (Exception $e) {
    echo "❌ 错误: " . $e->getMessage() . "\n";
}

echo "\n=== 演示完成 ===\n";
echo "\n💡 结论：\n";
echo "1. ❌ 不建议合并中间件 - 违反单一职责原则\n";
echo "2. ✅ 推荐优化分离模式 - 消除重复，保持职责清晰\n";
echo "3. 🔧 关键优化点：移除 StreamValidationPipe 中的重复检查\n";
echo "4. 📈 优化效果：减少冗余，提高可维护性\n";
