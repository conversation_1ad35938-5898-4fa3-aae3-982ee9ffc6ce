# PhpFFMpegRecorder $media->save() 同步执行分析

## 调用链分析

通过深入分析源码，我们可以清楚地看到 `$media->save()` 方法的完整调用链：

```
PhpFFMpegRecorder->start()
    ↓
Audio->save()                           // vendor/php-ffmpeg/php-ffmpeg/src/FFMpeg/Media/Audio.php:61
    ↓
AbstractBinary->command()               // vendor/php-ffmpeg/php-ffmpeg/src/Alchemy/BinaryDriver/AbstractBinary.php:130
    ↓
AbstractBinary->run()                   // vendor/php-ffmpeg/php-ffmpeg/src/Alchemy/BinaryDriver/AbstractBinary.php:191
    ↓
ProcessRunner->run()                    // vendor/php-ffmpeg/php-ffmpeg/src/Alchemy/BinaryDriver/ProcessRunner.php:55
    ↓
Symfony\Component\Process\Process->run() // vendor/symfony/process/Process.php:247
    ↓
Process->start() + Process->wait()      // vendor/symfony/process/Process.php:249-251
```

## 关键代码分析

### 1. Audio->save() 方法

**文件**: `vendor/php-ffmpeg/php-ffmpeg/src/FFMpeg/Media/Audio.php:61-79`

```php
public function save(FormatInterface $format, $outputPathfile)
{
    $listeners = null;

    if ($format instanceof ProgressableInterface) {
        $listeners = $format->createProgressListener($this, $this->ffprobe, 1, 1, 0);
    }

    $commands = $this->buildCommand($format, $outputPathfile);

    try {
        // 🔴 关键调用：这里会阻塞直到命令执行完成
        $this->driver->command($commands, false, $listeners);
    } catch (ExecutionFailureException $e) {
        $this->cleanupTemporaryFile($outputPathfile);
        throw new RuntimeException('Encoding failed', $e->getCode(), $e);
    }

    return $this;
}
```

**分析**：
- `$this->driver->command()` 调用是同步的
- 方法会等待 FFmpeg 命令完全执行完成才返回
- 如果录制时长很长，这里会阻塞相应的时间

### 2. AbstractBinary->command() 方法

**文件**: `vendor/php-ffmpeg/php-ffmpeg/src/Alchemy/BinaryDriver/AbstractBinary.php:130-137`

```php
public function command($command, $bypassErrors = false, $listeners = null)
{
    if (!is_array($command)) {
        $command = array($command);
    }

    // 🔴 直接调用 run 方法，没有异步处理
    return $this->run($this->factory->create($command), $bypassErrors, $listeners);
}
```

**分析**：
- 直接调用 `run()` 方法，没有任何异步包装
- 返回值依赖于 `run()` 方法的执行结果

### 3. AbstractBinary->run() 方法

**文件**: `vendor/php-ffmpeg/php-ffmpeg/src/Alchemy/BinaryDriver/AbstractBinary.php:191-208`

```php
protected function run(Process $process, $bypassErrors = false, $listeners = null)
{
    // ... 监听器设置代码 ...

    // 🔴 关键调用：委托给 ProcessRunner
    return $this->processRunner->run($process, $listenersManager->storage, $bypassErrors);
}
```

**分析**：
- 将执行委托给 `ProcessRunner->run()`
- 没有异步处理逻辑

### 4. ProcessRunner->run() 方法

**文件**: `vendor/php-ffmpeg/php-ffmpeg/src/Alchemy/BinaryDriver/ProcessRunner.php:55-80`

```php
public function run(Process $process, SplObjectStorage $listeners, $bypassErrors)
{
    $this->logger->info(sprintf(
        '%s running command %s',
        $this->name,
        $process->getCommandLine()
    ));

    try {
        // 🔴 关键调用：Symfony Process 的同步 run 方法
        $process->run($this->buildCallback($listeners));
    } catch (RuntimeException $e) {
        if (!$bypassErrors) {
            $this->doExecutionFailure($process->getCommandLine(), $process->getErrorOutput(), $e);
        }
    }

    // ... 错误处理和返回逻辑 ...
    
    return $process->getOutput();
}
```

**分析**：
- 直接调用 `$process->run()`，这是 Symfony Process 的**同步方法**
- 会阻塞直到进程执行完成

### 5. Symfony Process->run() 方法

**文件**: `vendor/symfony/process/Process.php:247-252`

```php
public function run(?callable $callback = null, array $env = []): int
{
    $this->start($callback, $env);

    // 🔴 关键调用：wait() 方法会阻塞等待进程完成
    return $this->wait();
}
```

**分析**：
- `start()` 启动进程
- `wait()` **阻塞等待**进程完成
- 这是标准的同步执行模式

### 6. Symfony Process->wait() 方法

**文件**: `vendor/symfony/process/Process.php:408-438`

```php
public function wait(?callable $callback = null): int
{
    $this->requireProcessIsStarted(__FUNCTION__);
    $this->updateStatus(false);

    // ... 回调处理 ...

    // 🔴 关键循环：阻塞等待进程运行完成
    do {
        $this->checkTimeout();
        $running = $this->isRunning() && ('\\' === \DIRECTORY_SEPARATOR || $this->processPipes->areOpen());
        $this->readPipes($running, '\\' !== \DIRECTORY_SEPARATOR || !$running);
    } while ($running);

    // 🔴 额外的等待循环
    while ($this->isRunning()) {
        $this->checkTimeout();
        usleep(1000);  // 1ms 的微睡眠
    }

    return $this->exitcode;
}
```

**分析**：
- 两个 `while` 循环确保进程完全结束
- `usleep(1000)` 表明这是**主动轮询等待**
- 直到进程完全结束才返回退出码

## 结论：$media->save() 是完全同步执行

### 证据总结

1. **调用链中没有异步处理**：
   - 从 `Audio->save()` 到 `Process->wait()` 的整个调用链都是同步的
   - 没有任何地方使用异步机制（如回调、Promise、协程等）

2. **Symfony Process 使用同步模式**：
   - 使用的是 `Process->run()` 而不是 `Process->start()`
   - `run()` 方法内部调用 `wait()` 进行阻塞等待

3. **阻塞等待机制**：
   - `Process->wait()` 中的两个 `while` 循环会一直等待进程结束
   - 使用 `usleep(1000)` 进行轮询，这是典型的同步等待模式

4. **返回值依赖**：
   - 每一层的返回都依赖于下一层的完成
   - 没有立即返回句柄或 Promise 的异步模式

### 对 Swoole 协程的影响

```php
// 在 SwooleRecordingManager 中
Coroutine::create(function () use ($url) {
    // 这里会调用 PhpFFMpegRecorder->start()
    $recordrConnector->handle($platform, $callback);
    
    // ❌ 上面的调用会阻塞整个协程数小时
    // ❌ 协程调度器无法切换到其他协程
    // ❌ 失去了协程并发的优势
});
```

### 与 NativeFFmpegRecorder 的对比

| 特性 | PhpFFMpegRecorder | NativeFFmpegRecorder |
|------|-------------------|----------------------|
| **执行方式** | `Process->run()` (同步) | `Process->start()` (异步) |
| **返回时机** | 录制完成后返回 | 立即返回进程句柄 |
| **协程友好** | ❌ 完全阻塞 | ✅ 非阻塞 |
| **进程控制** | ❌ 无法控制 | ✅ 可控制启停 |

### 最终建议

1. **避免在 Swoole 协程中使用 PhpFFMpegRecorder**
2. **推荐使用 NativeFFmpegRecorder** 进行异步录制
3. **如果必须使用 PhpFFMpegRecorder**，建议：
   - 使用 ProcessRecordingManager（进程隔离）
   - 或者实现异步包装器
   - 避免在协程环境中直接使用

**核心原因**：PhpFFMpegRecorder 的 `$media->save()` 方法通过 Symfony Process 的 `run()` 方法进行同步执行，会阻塞当前线程/协程直到 FFmpeg 进程完全结束，这与 Swoole 协程的异步特性完全冲突。
