<?php

declare(strict_types=1);

require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/ProcessRecordingManager.php';

use Examples\ProcessRecordingManager;
use Examples\Config\ProcessRecordingConfig;

/**
 * 进程录制管理器使用示例
 * 
 * 演示如何使用 ProcessRecordingManager 替代 SwooleRecordingManager
 */

echo "=== 进程录制管理器示例 ===\n\n";

try {
    // 1. 基本使用示例
    echo "1. 基本使用示例\n";
    echo "----------------\n";
    
    $basicConfig = [
        'urls' => [
            'https://live.douyin.com/94400148976',
            'https://live.douyin.com/123456789',
            // 可以添加更多URL进行测试
        ],
        'process' => [
            'max_processes' => 3,
            'process_timeout' => 30, // 30秒超时用于演示
            'enable_monitoring' => true,
            'monitoring_interval' => 2,
        ],
        'recording' => [
            'save_path' => '/tmp/recordings',
            'timeout' => 10,
            'max_retries' => 2,
        ],
        'logging' => [
            'enable' => true,
            'include_memory_usage' => true,
            'include_process_id' => true,
        ],
    ];
    
    $manager = new ProcessRecordingManager($basicConfig);
    
    echo "配置已加载，开始录制...\n";
    $manager->startRecording();
    
    echo "\n录制完成！\n\n";
    
} catch (\Throwable $e) {
    echo "错误: " . $e->getMessage() . "\n";
    echo "堆栈跟踪: " . $e->getTraceAsString() . "\n\n";
}

try {
    // 2. 从 Swoole 配置转换示例
    echo "2. 从 Swoole 配置转换示例\n";
    echo "-------------------------\n";
    
    // 模拟原有的 Swoole 配置
    $swooleConfig = [
        'urls' => [
            'https://live.douyin.com/test1',
            'https://live.douyin.com/test2',
        ],
        'swoole' => [
            'max_coroutines' => 5,
            'enable_debug' => true,
            'memory_limit' => 50 * 1024 * 1024,
            'gc_interval' => 30,
            'enable_monitoring' => true,
        ],
        'recording' => [
            'save_path' => '/tmp/swoole_recordings',
            'max_retries' => 3,
            'retry_interval' => 2000,
        ],
        'logging' => [
            'enable' => true,
            'include_coroutine_id' => true,
        ],
    ];
    
    // 转换配置
    $processConfig = ProcessRecordingConfig::convertFromSwooleConfig($swooleConfig);
    
    echo "Swoole 配置已转换为进程配置:\n";
    echo "- 最大协程数 -> 最大进程数: " . $processConfig['process']['max_processes'] . "\n";
    echo "- 协程ID -> 进程ID: " . ($processConfig['logging']['include_process_id'] ? '是' : '否') . "\n";
    echo "- 监控功能: " . ($processConfig['process']['enable_monitoring'] ? '启用' : '禁用') . "\n\n";
    
    $convertedManager = new ProcessRecordingManager($processConfig);
    echo "转换后的管理器已创建\n\n";
    
} catch (\Throwable $e) {
    echo "转换配置错误: " . $e->getMessage() . "\n\n";
}

try {
    // 3. 环境变量配置示例
    echo "3. 环境变量配置示例\n";
    echo "------------------\n";
    
    // 设置一些环境变量用于演示
    $_ENV['PROCESS_MAX_PROCESSES'] = '8';
    $_ENV['PROCESS_TIMEOUT'] = '1800';
    $_ENV['RECORDING_SAVE_PATH'] = '/tmp/env_recordings';
    $_ENV['LOGGING_LEVEL'] = 'debug';
    
    $envConfig = ProcessRecordingConfig::getEnvConfig();
    $mergedConfig = ProcessRecordingConfig::mergeConfig($envConfig);
    
    echo "环境变量配置已加载:\n";
    echo "- 最大进程数: " . $mergedConfig['process']['max_processes'] . "\n";
    echo "- 进程超时: " . $mergedConfig['process']['process_timeout'] . "秒\n";
    echo "- 保存路径: " . $mergedConfig['recording']['save_path'] . "\n";
    echo "- 日志级别: " . $mergedConfig['logging']['level'] . "\n\n";
    
} catch (\Throwable $e) {
    echo "环境变量配置错误: " . $e->getMessage() . "\n\n";
}

try {
    // 4. 统计信息示例
    echo "4. 统计信息示例\n";
    echo "---------------\n";
    
    $statsConfig = [
        'urls' => [
            'https://live.douyin.com/stats_test1',
            'https://live.douyin.com/stats_test2',
            'https://live.douyin.com/stats_test3',
        ],
        'process' => [
            'max_processes' => 2,
            'process_timeout' => 5, // 短超时用于快速演示
            'enable_monitoring' => false, // 禁用监控以简化输出
        ],
        'logging' => [
            'enable' => true,
        ],
    ];
    
    $statsManager = new ProcessRecordingManager($statsConfig);
    
    echo "获取初始统计信息:\n";
    $initialStats = $statsManager->getStatistics();
    foreach ($initialStats as $key => $value) {
        echo "- {$key}: {$value}\n";
    }
    
    echo "\n开始录制以生成统计信息...\n";
    // 注意：这里会快速失败因为URL是测试用的
    $statsManager->startRecording();
    
    echo "\n获取最终统计信息:\n";
    $finalStats = $statsManager->getStatistics();
    foreach ($finalStats as $key => $value) {
        echo "- {$key}: {$value}\n";
    }
    
} catch (\Throwable $e) {
    echo "统计信息示例错误: " . $e->getMessage() . "\n\n";
}

echo "\n=== 示例完成 ===\n";
echo "\n使用说明:\n";
echo "1. ProcessRecordingManager 完全替代了 SwooleRecordingManager\n";
echo "2. 使用 Symfony Process 组件替代 Swoole 协程\n";
echo "3. 保持了相同的公共接口和配置结构\n";
echo "4. 支持从 Swoole 配置自动转换\n";
echo "5. 提供完整的监控、统计和错误处理功能\n";
echo "\n主要差异:\n";
echo "- max_coroutines -> max_processes\n";
echo "- 协程ID -> 进程ID\n";
echo "- 协程池 -> 进程池\n";
echo "- 协程监控 -> 进程监控\n";
