<?php

declare(strict_types=1);

/**
 * Swoole 协程录制连接器 - 优化版本
 *
 * 这个文件演示了如何使用 Swoole 协程并发处理多个直播流录制任务。
 * 相比原版本，这个优化版本提供了：
 *
 * - 完善的错误处理和异常管理
 * - 资源管理和内存监控
 * - 配置外部化和管理
 * - 使用专业的调试中间件
 * - 协程池和并发控制
 * - 详细的日志和监控
 *
 * <AUTHOR> Team
 * @version 2.0.0
 */

require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/SwooleRecordingManager.php';

use Examples\SwooleRecordingManager;
use Examples\Config\SwooleRecordingConfig;


// 主程序入口
try {
    echo "=== Swoole 协程录制连接器 - 优化版本 ===\n";
    echo "启动时间: " . date('Y-m-d H:i:s') . "\n\n";

    // 获取配置（可以通过环境变量覆盖默认配置）
    $envConfig = SwooleRecordingConfig::getEnvConfig();
    $config = SwooleRecordingConfig::mergeConfig($envConfig);

    // 可以通过命令行参数调整配置
    if (isset($argv[1]) && is_numeric($argv[1])) {
        $config['swoole']['max_coroutines'] = (int)$argv[1];
        echo "通过命令行设置最大协程数: {$config['swoole']['max_coroutines']}\n";
    }

    if (isset($argv[2])) {
        $config['recording']['save_path'] = $argv[2];
        echo "通过命令行设置保存路径: {$config['recording']['save_path']}\n";
    }

    echo "\n";

    // 创建并启动录制管理器
    $manager = new SwooleRecordingManager($config);

    // 注册信号处理器
    if (function_exists('pcntl_signal')) {
        pcntl_signal(SIGINT, function () {
            echo "\n收到中断信号，正在优雅关闭...\n";
            exit(0);
        });
        pcntl_signal(SIGTERM, function () {
            echo "\n收到终止信号，正在优雅关闭...\n";
            exit(0);
        });
    }

    // 注册关闭处理器
    register_shutdown_function(function () {
        echo "\n程序结束，清理资源...\n";
        if (function_exists('gc_collect_cycles')) {
            gc_collect_cycles();
        }
        echo "资源清理完成\n";
    });

    // 开始录制
    $manager->startRecording();

    echo "\n=== 所有录制任务完成 ===\n";
    echo "结束时间: " . date('Y-m-d H:i:s') . "\n";
} catch (\Throwable $e) {
    echo "\n❌ 程序异常: " . $e->getMessage() . "\n";
    echo "异常类型: " . get_class($e) . "\n";
    echo "文件位置: " . $e->getFile() . ":" . $e->getLine() . "\n";

    if (isset($config['swoole']['enable_debug']) && $config['swoole']['enable_debug']) {
        echo "\n调试信息:\n" . $e->getTraceAsString() . "\n";
    }

    exit(1);
}

/**
 * 使用说明:
 *
 * 基本使用:
 * php examples/SwooleRecordrConnector.php
 *
 * 指定最大协程数:
 * php examples/SwooleRecordrConnector.php 3
 *
 * 指定最大协程数和保存路径:
 * php examples/SwooleRecordrConnector.php 3 /custom/path
 *
 * 使用环境变量:
 * SWOOLE_MAX_COROUTINES=3 RECORDING_SAVE_PATH=/custom/path php examples/SwooleRecordrConnector.php
 *
 * 环境变量说明:
 * - RECORDING_SAVE_PATH: 录制文件保存路径
 * - RECORDING_MAX_RETRIES: 最大重试次数
 * - RECORDING_RETRY_INTERVAL: 重试间隔（毫秒）
 * - SWOOLE_MAX_COROUTINES: 最大协程数
 * - SWOOLE_DEBUG: 是否启用调试模式
 * - SWOOLE_MEMORY_LIMIT: 内存限制（字节）
 * - LOGGING_ENABLE: 是否启用日志
 * - LOGGING_LEVEL: 日志级别
 *
 * 优化特性:
 * - ✅ 使用专业的 DebugInfoPipe 中间件
 * - ✅ 完善的异常处理和重试机制
 * - ✅ 协程池控制并发数量
 * - ✅ 内存监控和垃圾回收
 * - ✅ 详细的日志和统计信息
 * - ✅ 配置外部化和环境变量支持
 * - ✅ 信号处理和优雅关闭
 * - ✅ 资源管理和清理
 */
