<?php

declare(strict_types=1);

namespace Examples;

require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/config/ProcessRecordingConfig.php';
require_once __DIR__ . '/ProcessPoolManager.php';

use Examples\Config\ProcessRecordingConfig;
use Examples\ProcessPoolManager;
use LiveStream\PlatformFactory;
use LiveStream\Platforms\PlatformManager;
use LiveStream\Config\RecordingOptions;
use LiveStream\Recording\RecordrConnector;
use LiveStream\Recording\Pipes\DebugInfoPipe;
use Symfony\Component\Process\Process;

/**
 * 优化的进程录制管理器
 * 
 * 展示避免临时文件的替代方案和最佳实践
 */
class OptimizedProcessRecordingManager
{
    private array $config;
    private PlatformManager $platformManager;
    private ProcessPoolManager $processPool;
    private array $statistics = [
        'total_tasks' => 0,
        'completed_tasks' => 0,
        'failed_tasks' => 0,
        'start_time' => 0,
        'memory_peak' => 0,
    ];

    /**
     * 静态工作脚本路径（避免重复创建）
     */
    private static ?string $workerScriptPath = null;

    public function __construct(array $config = [])
    {
        $this->config = ProcessRecordingConfig::mergeConfig($config);
        ProcessRecordingConfig::validateConfig($this->config);

        $this->platformManager = new PlatformManager(new PlatformFactory());
        $this->processPool = new ProcessPoolManager(
            $this->config['process']['max_processes'],
            $this->config['process']['process_timeout'],
            $this->config['process']['poll_interval']
        );

        $this->statistics['start_time'] = microtime(true);
        $this->ensureWorkerScript();

        $this->log('info', 'OptimizedProcessRecordingManager 初始化完成', [
            'max_processes' => $this->config['process']['max_processes'],
            'total_urls' => count($this->config['urls']),
            'worker_script' => self::$workerScriptPath,
        ]);
    }

    /**
     * 方案1：使用预创建的工作脚本（推荐）
     * 
     * 优势：
     * - 只创建一次脚本文件
     * - 更好的性能
     * - 便于调试和维护
     */
    private function ensureWorkerScript(): void
    {
        if (self::$workerScriptPath === null || !file_exists(self::$workerScriptPath)) {
            self::$workerScriptPath = $this->createPermanentWorkerScript();
        }
    }

    private function createPermanentWorkerScript(): string
    {
        $scriptPath = __DIR__ . '/workers/recording_worker.php';
        $scriptDir = dirname($scriptPath);

        // 确保目录存在
        if (!is_dir($scriptDir)) {
            mkdir($scriptDir, 0755, true);
        }

        $scriptContent = $this->generateOptimizedWorkerScript();
        file_put_contents($scriptPath, $scriptContent);

        return $scriptPath;
    }

    /**
     * 方案2：使用 PHP 内置服务器（实验性）
     * 
     * 这是一个创新的方案，避免文件系统操作
     */
    private function createUrlProcessViaBuiltinServer(string $url, string $identifier): void
    {
        // 序列化任务数据
        $taskData = [
            'url' => $url,
            'identifier' => $identifier,
            'config' => $this->config,
            'timestamp' => time(),
        ];

        $serializedData = base64_encode(serialize($taskData));

        // 使用 PHP 的 -r 参数执行内联代码
        $inlineCode = sprintf(
            'require_once "%s"; $task = unserialize(base64_decode("%s")); (new %s())->executeTask($task);',
            __DIR__ . '/../vendor/autoload.php',
            $serializedData,
            self::class
        );

        $process = new Process(['php', '-r', $inlineCode]);
        $this->processPool->addProcess($process, $identifier);
    }

    /**
     * 方案3：使用标准输入传递数据
     * 
     * 避免命令行参数长度限制
     */
    private function createUrlProcessViaStdin(string $url, string $identifier): void
    {
        $taskData = [
            'url' => $url,
            'identifier' => $identifier,
            'config' => $this->config,
        ];

        $process = new Process(['php', self::$workerScriptPath, '--stdin']);
        $process->setInput(json_encode($taskData));
        
        $this->processPool->addProcess($process, $identifier);
    }

    /**
     * 主要的录制方法（使用方案1）
     */
    public function startRecording(): void
    {
        $urls = $this->config['urls'];
        $this->statistics['total_tasks'] = count($urls);

        $this->log('info', '开始录制任务', [
            'total_urls' => count($urls),
            'method' => 'permanent_worker_script',
        ]);

        foreach ($urls as $index => $url) {
            $identifier = "url_{$index}_" . time();
            $this->createUrlProcessViaPermanentScript($url, $identifier);
        }

        $this->processPool->waitForAll(function ($stats) {
            $this->updateStatisticsFromPool($stats);
        });

        $this->logFinalStatistics();
    }

    /**
     * 使用永久工作脚本创建进程
     */
    private function createUrlProcessViaPermanentScript(string $url, string $identifier): void
    {
        $process = new Process([
            'php',
            self::$workerScriptPath,
            $url,
            $identifier,
            json_encode($this->config)
        ]);

        $this->log('info', "创建URL处理进程", [
            'url' => $this->maskUrl($url),
            'identifier' => $identifier,
            'method' => 'permanent_script',
        ]);

        $this->processPool->addProcess($process, $identifier);
    }

    /**
     * 生成优化的工作脚本
     */
    private function generateOptimizedWorkerScript(): string
    {
        return <<<'PHP_SCRIPT'
#!/usr/bin/env php
<?php
/**
 * 录制工作进程脚本
 * 
 * 这是一个永久的工作脚本，避免重复创建临时文件
 * 支持多种输入方式：命令行参数、标准输入等
 */

declare(strict_types=1);

// 自动加载
$autoloaderPaths = [
    __DIR__ . '/../../vendor/autoload.php',
    __DIR__ . '/../vendor/autoload.php',
    __DIR__ . '/vendor/autoload.php',
];

foreach ($autoloaderPaths as $path) {
    if (file_exists($path)) {
        require_once $path;
        break;
    }
}

use LiveStream\PlatformFactory;
use LiveStream\Platforms\PlatformManager;
use LiveStream\Config\RecordingOptions;
use LiveStream\Recording\RecordrConnector;
use LiveStream\Recording\Pipes\DebugInfoPipe;

/**
 * 工作进程类
 */
class RecordingWorker
{
    public function run(): void
    {
        try {
            $taskData = $this->parseInput();
            $this->executeRecording($taskData);
            exit(0);
        } catch (\Throwable $e) {
            $this->logError($e);
            exit(1);
        }
    }

    private function parseInput(): array
    {
        global $argv, $argc;

        // 方式1：从命令行参数读取
        if ($argc >= 4 && !in_array('--stdin', $argv)) {
            return [
                'url' => $argv[1],
                'identifier' => $argv[2],
                'config' => json_decode($argv[3], true),
            ];
        }

        // 方式2：从标准输入读取
        if (in_array('--stdin', $argv)) {
            $input = stream_get_contents(STDIN);
            return json_decode($input, true);
        }

        throw new \InvalidArgumentException('Invalid input method');
    }

    private function executeRecording(array $taskData): void
    {
        $url = $taskData['url'];
        $identifier = $taskData['identifier'];
        $config = $taskData['config'];

        $startTime = microtime(true);
        echo "[Worker {$identifier}] Starting recording\n";

        // 创建平台管理器
        $platformManager = new PlatformManager(new PlatformFactory());
        $platform = $platformManager->driver($url);

        // 创建录制配置
        $options = new RecordingOptions();
        $options->setSavePath($config['recording']['save_path']);
        $options->setFormat($config['recording']['format']);
        $options->set([
            'timeout' => $config['recording']['timeout'],
            'max_retries' => $config['recording']['max_retries'],
            'custom_headers' => $config['recording']['custom_headers'],
        ]);

        // 创建录制连接器
        $recordrConnector = new RecordrConnector();
        $recordrConnector->withConfig($options);

        if (!empty($config['process']['enable_debug'])) {
            $recordrConnector->middleware()->pipe(new DebugInfoPipe());
            $recordrConnector->withDebug(true);
        }

        $recordrConnector
            ->withTries($config['recording']['max_retries'])
            ->withRetryInterval($config['recording']['retry_interval'])
            ->withExponentialBackoff($config['recording']['exponential_backoff']);

        // 执行录制
        $recordrConnector->handle($platform, function (string $type, string $buffer) use ($identifier) {
            $trimmed = trim($buffer);
            if (!empty($trimmed)) {
                echo "[Worker {$identifier}][{$type}]: {$trimmed}\n";
            }
        });

        $duration = round(microtime(true) - $startTime, 2);
        echo "[Worker {$identifier}] Completed in {$duration}s\n";
    }

    private function logError(\Throwable $e): void
    {
        fwrite(STDERR, "Worker Error: " . $e->getMessage() . "\n");
        fwrite(STDERR, "File: " . $e->getFile() . ":" . $e->getLine() . "\n");
    }
}

// 运行工作进程
(new RecordingWorker())->run();
PHP_SCRIPT;
    }

    // 其他辅助方法...
    private function updateStatisticsFromPool(array $poolStats): void
    {
        $this->statistics['completed_tasks'] = $poolStats['completed'];
        $this->statistics['failed_tasks'] = $poolStats['failed'];
    }

    private function logFinalStatistics(): void
    {
        $runtime = microtime(true) - $this->statistics['start_time'];
        $this->log('info', '录制任务完成', [
            'total_runtime' => round($runtime, 2) . 's',
            'completed_tasks' => $this->statistics['completed_tasks'],
            'failed_tasks' => $this->statistics['failed_tasks'],
            'total_tasks' => $this->statistics['total_tasks'],
        ]);
    }

    private function log(string $level, string $message, array $context = []): void
    {
        if (!$this->config['logging']['enable']) {
            return;
        }

        $timestamp = date('Y-m-d H:i:s');
        $contextStr = !empty($context) ? ' ' . json_encode($context, JSON_UNESCAPED_UNICODE) : '';
        
        printf("[%s] [%s] %s%s\n", $timestamp, strtoupper($level), $message, $contextStr);
    }

    private function maskUrl(string $url): string
    {
        $parsed = parse_url($url);
        return ($parsed['host'] ?? 'unknown') . ($parsed['path'] ?? '') . '?...';
    }

    public function __destruct()
    {
        // 清理资源，但保留永久工作脚本
        $this->processPool->cleanup();
    }
}
