<?php

declare(strict_types=1);

require_once __DIR__ . '/../vendor/autoload.php';

use LiveStream\Pipeline;

echo "=== 中间件执行顺序演示 ===\n\n";

// 创建管道实例
$pipeline = new Pipeline();

// 定义演示用的中间件
class DemoMiddleware
{
    private string $name;
    
    public function __construct(string $name)
    {
        $this->name = $name;
    }
    
    public function handle($passable, \Closure $next)
    {
        echo "🔄 进入中间件 {$this->name} (前置处理)\n";
        
        // 执行下一个中间件
        $result = $next($passable);
        
        echo "🔙 退出中间件 {$this->name} (后置处理)\n";
        
        return $result;
    }
}

// 演示1：基本的中间件执行顺序
echo "📋 演示1：基本的中间件执行顺序\n";
echo "注册顺序：A -> B -> C\n";
echo "执行顺序：A(前) -> B(前) -> C(前) -> 核心逻辑 -> C(后) -> B(后) -> A(后)\n\n";

$pipeline1 = new Pipeline();
$pipeline1->pipe(new DemoMiddleware('A'));
$pipeline1->pipe(new DemoMiddleware('B'));
$pipeline1->pipe(new DemoMiddleware('C'));

$result1 = $pipeline1->send('测试数据')->then(function($passable) {
    echo "🎯 执行核心逻辑，处理数据: {$passable}\n";
    return $passable . ' (已处理)';
});

echo "📤 最终结果: {$result1}\n\n";

echo str_repeat("=", 60) . "\n\n";

// 演示2：模拟 RecordrConnector 的中间件注册顺序
echo "📋 演示2：模拟 RecordrConnector 的中间件注册顺序\n";

class MockPendingRecorder
{
    private ?string $stream = null;
    
    public function getStream(): ?string
    {
        return $this->stream;
    }
    
    public function setStream(string $stream): void
    {
        $this->stream = $stream;
    }
}

class DebugMiddleware
{
    public function handle($pendingRecorder, \Closure $next)
    {
        echo "🐛 [DebugMiddleware] 前置：尝试访问流信息\n";
        
        // 模拟原始问题：在前置处理中访问流
        $stream = $pendingRecorder->getStream();
        if ($stream === null) {
            echo "❌ [DebugMiddleware] 流信息为空！\n";
        } else {
            echo "✅ [DebugMiddleware] 流信息: {$stream}\n";
        }
        
        $result = $next($pendingRecorder);
        
        echo "🐛 [DebugMiddleware] 后置：再次访问流信息\n";
        $stream = $pendingRecorder->getStream();
        if ($stream === null) {
            echo "❌ [DebugMiddleware] 流信息仍为空！\n";
        } else {
            echo "✅ [DebugMiddleware] 流信息: {$stream}\n";
        }
        
        return $result;
    }
}

class ValidateOptionsMiddleware
{
    public function handle($pendingRecorder, \Closure $next)
    {
        echo "🔍 [ValidateOptionsMiddleware] 验证配置选项\n";
        return $next($pendingRecorder);
    }
}

class StreamValidationMiddleware
{
    public function handle($pendingRecorder, \Closure $next)
    {
        echo "🌊 [StreamValidationMiddleware] 验证流并设置流信息\n";
        
        // 模拟流验证和设置
        $pendingRecorder->setStream('http://example.com/stream.m3u8');
        
        return $next($pendingRecorder);
    }
}

// 模拟原始问题的注册顺序
$pipeline2 = new Pipeline();

// 1. 示例文件中注册的调试中间件（第1个注册）
$pipeline2->pipe(new DebugMiddleware());

// 2. RecordrConnector::handle() 中注册的系统中间件
$pipeline2->pipe(new ValidateOptionsMiddleware());
$pipeline2->pipe(new StreamValidationMiddleware());

$pendingRecorder = new MockPendingRecorder();

echo "执行中间件管道...\n";
$pipeline2->send($pendingRecorder)->then(function($pendingRecorder) {
    echo "🎯 [核心逻辑] 开始录制流: " . $pendingRecorder->getStream() . "\n";
    return $pendingRecorder;
});

echo "\n" . str_repeat("=", 60) . "\n\n";

// 演示3：修复后的中间件模式
echo "📋 演示3：修复后的中间件模式\n";

class FixedDebugMiddleware
{
    public function handle($pendingRecorder, \Closure $next)
    {
        echo "🐛 [FixedDebugMiddleware] 前置：显示基本信息\n";
        echo "📝 [FixedDebugMiddleware] 录制准备中...\n";
        
        // 先执行下一个中间件
        $result = $next($pendingRecorder);
        
        echo "🐛 [FixedDebugMiddleware] 后置：显示流信息\n";
        $stream = $pendingRecorder->getStream();
        if ($stream === null) {
            echo "❌ [FixedDebugMiddleware] 流信息为空！\n";
        } else {
            echo "✅ [FixedDebugMiddleware] 流信息: {$stream}\n";
        }
        
        return $result;
    }
}

$pipeline3 = new Pipeline();
$pipeline3->pipe(new FixedDebugMiddleware());
$pipeline3->pipe(new ValidateOptionsMiddleware());
$pipeline3->pipe(new StreamValidationMiddleware());

$pendingRecorder2 = new MockPendingRecorder();

echo "执行修复后的中间件管道...\n";
$pipeline3->send($pendingRecorder2)->then(function($pendingRecorder) {
    echo "🎯 [核心逻辑] 开始录制流: " . $pendingRecorder->getStream() . "\n";
    return $pendingRecorder;
});

echo "\n" . str_repeat("=", 60) . "\n\n";

// 演示4：中间件注册顺序的影响
echo "📋 演示4：中间件注册顺序的影响\n";

echo "🔢 测试不同的注册顺序：\n\n";

// 顺序1：A -> B -> C
echo "注册顺序1：A -> B -> C\n";
$pipeline4a = new Pipeline();
$pipeline4a->pipe(new DemoMiddleware('A'));
$pipeline4a->pipe(new DemoMiddleware('B'));
$pipeline4a->pipe(new DemoMiddleware('C'));

$pipeline4a->send('数据')->then(function($data) {
    echo "🎯 核心逻辑\n";
    return $data;
});

echo "\n";

// 顺序2：C -> B -> A
echo "注册顺序2：C -> B -> A\n";
$pipeline4b = new Pipeline();
$pipeline4b->pipe(new DemoMiddleware('C'));
$pipeline4b->pipe(new DemoMiddleware('B'));
$pipeline4b->pipe(new DemoMiddleware('A'));

$pipeline4b->send('数据')->then(function($data) {
    echo "🎯 核心逻辑\n";
    return $data;
});

echo "\n=== 演示完成 ===\n";
echo "\n💡 关键要点：\n";
echo "1. 中间件按注册顺序执行（FIFO）\n";
echo "2. 每个中间件都有前置和后置处理阶段\n";
echo "3. 依赖数据应在后置处理阶段访问\n";
echo "4. 先注册的中间件在最外层，最先开始，最后结束\n";
