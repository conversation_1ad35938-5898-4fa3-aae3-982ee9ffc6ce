<?php

declare(strict_types=1);

namespace Examples;

require_once __DIR__ . '/../vendor/autoload.php';

use LiveStream\Recording\Drivers\RecorderInterface;
use LiveStream\Recording\PendingRecorder;
use LiveStream\Recording\RecordHandle;
use FFMpeg\FFMpeg;
use FFMpeg\Format\Video\X264;
use Symfony\Component\Process\Process;

/**
 * 优化的 PhpFFMpegRecorder 实现
 * 
 * 解决原版的以下问题：
 * 1. 双通道编码导致的性能浪费
 * 2. 同步阻塞问题
 * 3. 不必要的重复录制
 */
class OptimizedPhpFFMpegRecorder implements RecorderInterface
{
    /**
     * 方案一：优化的同步版本（解决双通道问题）
     */
    public function start(PendingRecorder $pendingRecorder, ?callable $progress = null): RecordHandle
    {
        try {
            $streamUrl = $pendingRecorder->getStream();
            
            // 1. 创建 FFMpeg 实例
            $ffmpeg = FFMpeg::create($pendingRecorder->recordrConnector()->config()->all());
            $media = $ffmpeg->open($streamUrl->getUrl());

            // 2. 🔴 关键优化：使用单通道流复制格式
            $format = $this->createOptimizedFormat();
            
            // 3. 设置进度回调
            if ($progress !== null) {
                $format->on('progress', function ($media, $format, $percentage) use ($progress) {
                    $progress('stdout', "progress: {$percentage}%");
                });
            }

            // 4. 🔴 优化：执行单通道录制（仍然是同步的，但减少了50%时间）
            $media->save($format, $pendingRecorder->savePath());

            // 5. 返回已完成的句柄
            return new RecordHandle(
                recordId: $pendingRecorder->getRecordId(),
                outputPath: $pendingRecorder->getOutputPath(),
                command: ['php-ffmpeg', 'completed'],
                process: null
            );

        } catch (\Throwable $e) {
            throw $e;
        }
    }

    /**
     * 创建优化的格式配置
     */
    private function createOptimizedFormat(): X264
    {
        $format = new X264('aac', 'libx264');
        
        // 🔴 关键优化1：强制单通道编码
        $format->setPasses(1);
        
        // 🔴 关键优化2：设置比特率为0也会触发单通道
        $format->setKiloBitrate(0);
        $format->setAudioKiloBitrate(128);
        
        // 🔴 关键优化3：流复制参数（避免重新编码）
        $additionalParams = [
            '-c:v', 'copy',      // 视频流复制
            '-c:a', 'copy',      // 音频流复制
            '-map', '0',         // 映射所有流
            '-f', 'mpegts',      // 使用 TS 格式
            '-avoid_negative_ts', 'make_zero',  // 时间戳处理
            '-fflags', '+genpts' // 生成时间戳
        ];
        $format->setAdditionalParameters($additionalParams);
        
        return $format;
    }
}

/**
 * 异步版本的 PhpFFMpegRecorder（推荐）
 * 
 * 通过提取命令并异步执行来解决阻塞问题
 */
class AsyncPhpFFMpegRecorder implements RecorderInterface
{
    public function start(PendingRecorder $pendingRecorder, ?callable $progress = null): RecordHandle
    {
        try {
            // 1. 提取 FFmpeg 命令
            $command = $this->extractFFmpegCommand($pendingRecorder);
            
            // 2. 使用 Symfony Process 异步执行
            $process = new Process($command);
            $process->setTimeout(null);  // 无超时限制
            
            // 3. 启动异步进程
            $process->start();
            
            // 4. 设置进度回调（如果需要）
            if ($progress) {
                // 注意：这里不能调用 wait()，因为那会变成同步
                // 可以通过定时器或其他方式来读取进度
                $this->setupProgressCallback($process, $progress);
            }
            
            // 5. 立即返回可控制的句柄
            return new RecordHandle(
                recordId: $pendingRecorder->getRecordId(),
                outputPath: $pendingRecorder->getOutputPath(),
                command: $command,
                process: $process
            );

        } catch (\Throwable $e) {
            throw $e;
        }
    }

    /**
     * 提取 php-ffmpeg 生成的 FFmpeg 命令
     */
    private function extractFFmpegCommand(PendingRecorder $pendingRecorder): array
    {
        try {
            $streamUrl = $pendingRecorder->getStream();
            
            // 1. 创建临时的 FFMpeg 实例来生成命令
            $ffmpeg = FFMpeg::create($pendingRecorder->recordrConnector()->config()->all());
            $media = $ffmpeg->open($streamUrl->getUrl());
            
            // 2. 创建优化的格式
            $format = $this->createOptimizedFormat();
            
            // 3. 🔴 关键：使用 getFinalCommand 获取命令而不执行
            $commands = $media->getFinalCommand($format, $pendingRecorder->savePath());
            
            // 4. 返回第一个命令（单通道）
            if (empty($commands)) {
                throw new \RuntimeException("无法生成 FFmpeg 命令");
            }
            
            // 将命令字符串分解为数组
            return $this->parseCommandString($commands[0]);
            
        } catch (\Throwable $e) {
            throw new \RuntimeException("命令提取失败: " . $e->getMessage(), 0, $e);
        }
    }

    /**
     * 创建优化的格式配置
     */
    private function createOptimizedFormat(): X264
    {
        $format = new X264('aac', 'libx264');
        $format->setPasses(1);           // 强制单通道
        $format->setKiloBitrate(0);      // 触发单通道模式
        $format->setAudioKiloBitrate(128);
        
        $additionalParams = [
            '-c:v', 'copy',
            '-c:a', 'copy',
            '-map', '0',
            '-f', 'mpegts',
            '-avoid_negative_ts', 'make_zero',
            '-fflags', '+genpts'
        ];
        $format->setAdditionalParameters($additionalParams);
        
        return $format;
    }

    /**
     * 解析命令字符串为数组
     */
    private function parseCommandString(string $commandString): array
    {
        // 简单的命令解析（可以根据需要改进）
        $parts = explode(' ', $commandString);
        
        // 过滤空字符串
        return array_filter($parts, fn($part) => !empty(trim($part)));
    }

    /**
     * 设置进度回调（非阻塞方式）
     */
    private function setupProgressCallback(Process $process, callable $progress): void
    {
        // 这里可以实现非阻塞的进度监控
        // 例如使用定时器定期检查进程输出
        // 或者使用 ReactPHP 等异步库
        
        // 简单示例：启动一个后台任务来监控进度
        if (function_exists('pcntl_fork')) {
            $pid = pcntl_fork();
            if ($pid === 0) {
                // 子进程中监控进度
                while ($process->isRunning()) {
                    $output = $process->getIncrementalOutput();
                    if (!empty($output)) {
                        $progress('stdout', $output);
                    }
                    usleep(100000); // 100ms
                }
                exit(0);
            }
        }
    }
}

/**
 * 命令提取工具类
 * 
 * 独立的工具类，用于提取 php-ffmpeg 生成的命令
 */
class PhpFFMpegCommandExtractor
{
    /**
     * 提取优化的 FFmpeg 命令
     */
    public static function extractCommand(
        string $inputUrl,
        string $outputPath,
        array $config = [],
        array $headers = []
    ): array {
        try {
            // 1. 创建 FFMpeg 实例
            $ffmpeg = FFMpeg::create($config);
            $media = $ffmpeg->open($inputUrl);
            
            // 2. 创建单通道流复制格式
            $format = new X264('aac', 'libx264');
            $format->setPasses(1);
            $format->setKiloBitrate(0);
            $format->setAudioKiloBitrate(128);
            
            $additionalParams = [
                '-c:v', 'copy',
                '-c:a', 'copy',
                '-map', '0',
                '-f', 'mpegts'
            ];
            
            // 添加请求头支持
            if (!empty($headers)) {
                $headerLines = [];
                foreach ($headers as $k => $v) {
                    $headerLines[] = sprintf('%s: %s', $k, $v);
                }
                array_unshift($additionalParams, '-headers', implode('\r\n', $headerLines));
            }
            
            $format->setAdditionalParameters($additionalParams);
            
            // 3. 获取命令
            $commands = $media->getFinalCommand($format, $outputPath);
            
            if (empty($commands)) {
                throw new \RuntimeException("无法生成命令");
            }
            
            // 4. 解析并返回命令数组
            return explode(' ', $commands[0]);
            
        } catch (\Throwable $e) {
            throw new \RuntimeException("命令提取失败: " . $e->getMessage(), 0, $e);
        }
    }

    /**
     * 构建简化的流复制命令（不依赖 php-ffmpeg）
     */
    public static function buildStreamCopyCommand(
        string $inputUrl,
        string $outputPath,
        array $headers = []
    ): array {
        $command = ['ffmpeg', '-y'];
        
        // 网络参数
        $command = array_merge($command, [
            '-rw_timeout', '15000000',
            '-analyzeduration', '20000000',
            '-probesize', '10000000',
            '-protocol_whitelist', 'rtmp,crypto,file,http,https,tcp,tls,udp,rtp,httpproxy'
        ]);
        
        // 请求头
        if (!empty($headers)) {
            $headerLines = [];
            foreach ($headers as $k => $v) {
                $headerLines[] = sprintf('%s: %s', $k, $v);
            }
            $command[] = '-headers';
            $command[] = implode('\r\n', $headerLines);
        }
        
        // 输入输出
        $command = array_merge($command, [
            '-i', $inputUrl,
            '-c', 'copy',
            '-f', 'mpegts',
            '-avoid_negative_ts', 'make_zero',
            $outputPath
        ]);
        
        return $command;
    }
}
