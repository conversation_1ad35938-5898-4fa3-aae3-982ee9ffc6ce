<?php

declare(strict_types=1);

use LiveStream\Stream\Stream;
use LiveStream\Enum\Quality;
use Carbon\Carbon;

describe('Stream Class', function () {

    describe('Basic Properties', function () {
        test('should create stream with basic properties', function () {
            $stream = new Stream(
                'https://example.com/stream.m3u8',
                'hls',
                Quality::HD1
            );

            expect($stream->getUrl())->toBe('https://example.com/stream.m3u8');
            expect($stream->getType())->toBe('hls');
            expect($stream->getQuality())->toBe(Quality::HD1);
            expect($stream->isValidated())->toBeFalse();
        });

        test('should create stream with validation status', function () {
            $stream = new Stream(
                'https://example.com/stream.flv',
                'flv',
                Quality::ORIGIN,
                true
            );

            expect($stream->getUrl())->toBe('https://example.com/stream.flv');
            expect($stream->getType())->toBe('flv');
            expect($stream->getQuality())->toBe(Quality::ORIGIN);
            expect($stream->isValidated())->toBeTrue();
        });

        test('should create stream with null quality', function () {
            $stream = new Stream(
                'https://example.com/stream.m3u8',
                'hls',
                null
            );

            expect($stream->getQuality())->toBeNull();
        });
    });

    describe('URL Validation', function () {
        test('should validate valid HTTP URLs', function () {
            $stream = new Stream(
                'https://example.com/stream.m3u8',
                'hls',
                Quality::HD1
            );

            expect($stream->validateUrl())->toBeTrue();
        });

        test('should reject invalid URLs', function () {
            $stream = new Stream(
                'invalid-url',
                'hls',
                Quality::HD1
            );

            expect($stream->validateUrl())->toBeFalse();
        });

        test('should validate HTTPS URLs', function () {
            $stream = new Stream(
                'https://secure.example.com/stream.m3u8',
                'hls',
                Quality::HD1
            );

            expect($stream->validateUrl())->toBeTrue();
        });

        test('should validate HTTP URLs', function () {
            $stream = new Stream(
                'http://example.com/stream.flv',
                'flv',
                Quality::SD1
            );

            expect($stream->validateUrl())->toBeTrue();
        });
    });

    describe('Format Validation', function () {
        test('should validate HLS format correctly', function () {
            $hlsStream = new Stream(
                'https://example.com/playlist.m3u8',
                'hls',
                Quality::HD1
            );

            expect($hlsStream->validateFormat())->toBeTrue();
        });

        test('should validate FLV format correctly', function () {
            $flvStream = new Stream(
                'https://example.com/stream.flv',
                'flv',
                Quality::HD1
            );

            expect($flvStream->validateFormat())->toBeTrue();
        });

        test('should reject mismatched format and URL', function () {
            $mismatchedStream = new Stream(
                'https://example.com/stream.flv',
                'hls', // Type says HLS but URL is FLV
                Quality::HD1
            );

            expect($mismatchedStream->validateFormat())->toBeFalse();
        });

        test('should handle URLs without clear extension', function () {
            $noExtStream = new Stream(
                'https://example.com/api/stream?format=m3u8',
                'hls',
                Quality::HD1
            );

            expect($noExtStream->validateFormat())->toBeTrue();
        });
    });

    describe('Connection Validation', function () {
        test('should validate connection for accessible URLs', function () {
            // 注意：这个测试可能会因为网络问题而不稳定
            // 在实际项目中，应该mock HTTP请求
            $stream = new Stream(
                'https://httpbin.org/status/200',
                'hls',
                Quality::HD1
            );

            // 由于网络依赖，我们只测试方法存在且返回布尔值
            $result = $stream->validateConnection();
            expect($result)->toBeIn([true, false]);
        });

        test('should handle connection validation for invalid URLs', function () {
            $stream = new Stream(
                'https://nonexistent-domain-12345.com/stream.m3u8',
                'hls',
                Quality::HD1
            );

            expect($stream->validateConnection())->toBeFalse();
        });
    });

    describe('Complete Validation', function () {
        test('should pass complete validation for valid stream', function () {
            $stream = new Stream(
                'https://example.com/stream.m3u8',
                'hls',
                Quality::HD1
            );

            // Mock the connection validation to avoid network dependency
            $stream = new class('https://example.com/stream.m3u8', 'hls', Quality::HD1) extends Stream {
                public function validateConnection(): bool
                {
                    return true; // Mock successful connection
                }
            };

            expect($stream->validate())->toBeTrue();
            expect($stream->isValidated())->toBeTrue();
        });

        test('should fail complete validation for invalid stream', function () {
            $stream = new Stream(
                'invalid-url',
                'hls',
                Quality::HD1
            );

            expect($stream->validate())->toBeFalse();
            expect($stream->isValidated())->toBeFalse();
        });

        test('should update validation status after validation', function () {
            $stream = new class('https://example.com/stream.m3u8', 'hls', Quality::HD1) extends Stream {
                public function validateConnection(): bool
                {
                    return true;
                }
            };

            expect($stream->isValidated())->toBeFalse();
            $stream->validate();
            expect($stream->isValidated())->toBeTrue();
        });
    });

    describe('Metadata and Serialization', function () {
        test('should convert to array correctly', function () {
            $stream = new Stream(
                'https://example.com/stream.m3u8',
                'hls',
                Quality::HD1,
                true
            );

            $array = $stream->toArray();

            expect($array)->toBe([
                'url' => 'https://example.com/stream.m3u8',
                'type' => 'hls',
                'quality' => Quality::HD1,
                'validated' => true,
                'expire' => null,
                'expire_timestamp' => null,
            ]);
        });

        test('should convert to array with null quality', function () {
            $stream = new Stream(
                'https://example.com/stream.flv',
                'flv',
                null,
                false
            );

            $array = $stream->toArray();

            expect($array)->toBe([
                'url' => 'https://example.com/stream.flv',
                'type' => 'flv',
                'quality' => null,
                'validated' => false,
                'expire' => null,
                'expire_timestamp' => null,
            ]);
        });

        test('should convert to string correctly', function () {
            $stream = new Stream(
                'https://example.com/stream.m3u8',
                'hls',
                Quality::HD1
            );

            expect((string) $stream)->toBe('https://example.com/stream.m3u8');
        });
    });

    describe('Quality Information', function () {
        test('should handle different quality levels', function () {
            $qualities = [
                Quality::ORIGIN,
                Quality::FULL_HD1,
                Quality::HD1,
                Quality::SD1,
                Quality::SD2,
            ];

            foreach ($qualities as $quality) {
                $stream = new Stream(
                    'https://example.com/stream.m3u8',
                    'hls',
                    $quality
                );

                expect($stream->getQuality())->toBe($quality);
            }
        });

        test('should provide quality display information', function () {
            $stream = new Stream(
                'https://example.com/stream.m3u8',
                'hls',
                Quality::HD1
            );

            expect($stream->getQualityDisplayName())->toBe(Quality::HD1->getDisplayName());
        });
    });

    describe('Stream Type Information', function () {
        test('should identify HLS streams correctly', function () {
            $stream = new Stream(
                'https://example.com/stream.m3u8',
                'hls',
                Quality::HD1
            );

            expect($stream->isHls())->toBeTrue();
            expect($stream->isFlv())->toBeFalse();
        });

        test('should identify FLV streams correctly', function () {
            $stream = new Stream(
                'https://example.com/stream.flv',
                'flv',
                Quality::HD1
            );

            expect($stream->isHls())->toBeFalse();
            expect($stream->isFlv())->toBeTrue();
        });
    });

    describe('Expire Functionality', function () {
        test('should create stream without expire time', function () {
            $stream = new Stream(
                'https://example.com/stream.m3u8',
                'hls',
                Quality::HD1
            );

            expect($stream->getExpire())->toBeNull();
            expect($stream->isExpired())->toBeFalse();
            expect($stream->getTimeToExpire())->toBeNull();
        });

        test('should create stream with Carbon expire time', function () {
            $expireTime = Carbon::now()->addHours(2);
            $stream = new Stream(
                'https://example.com/stream.m3u8',
                'hls',
                Quality::HD1,
                false,
                $expireTime
            );

            expect($stream->getExpire())->toBeInstanceOf(Carbon::class);
            expect($stream->getExpire()->timestamp)->toBe($expireTime->timestamp);
            expect($stream->isExpired())->toBeFalse();
            expect($stream->getTimeToExpire())->toBeGreaterThan(0);
        });

        test('should create stream with timestamp expire time', function () {
            $timestamp = Carbon::now()->addHours(3)->timestamp;
            $stream = new Stream(
                'https://example.com/stream.m3u8',
                'hls',
                Quality::HD1,
                false,
                $timestamp
            );

            expect($stream->getExpire())->toBeInstanceOf(Carbon::class);
            expect($stream->getExpire()->timestamp)->toBe($timestamp);
            expect($stream->isExpired())->toBeFalse();
        });

        test('should handle expired stream', function () {
            $expiredTime = Carbon::now()->subHours(1);
            $stream = new Stream(
                'https://example.com/stream.m3u8',
                'hls',
                Quality::HD1,
                false,
                $expiredTime
            );

            expect($stream->getExpire())->toBeInstanceOf(Carbon::class);
            expect($stream->isExpired())->toBeTrue();
            expect($stream->getTimeToExpire())->toBe(0);
        });

        test('should handle invalid timestamp', function () {
            $stream = new Stream(
                'https://example.com/stream.m3u8',
                'hls',
                Quality::HD1,
                false,
                -1 // 无效时间戳
            );

            expect($stream->getExpire())->toBeNull();
            expect($stream->isExpired())->toBeFalse();
            expect($stream->getTimeToExpire())->toBeNull();
        });

        test('should set expire time using setter', function () {
            $stream = new Stream(
                'https://example.com/stream.m3u8',
                'hls',
                Quality::HD1
            );

            $expireTime = Carbon::now()->addHours(1);
            $result = $stream->setExpire($expireTime);

            expect($result)->toBe($stream); // 应该返回自身以支持链式调用
            expect($stream->getExpire())->toBeInstanceOf(Carbon::class);
            expect($stream->getExpire()->timestamp)->toBe($expireTime->timestamp);
        });

        test('should set expire time using timestamp', function () {
            $stream = new Stream(
                'https://example.com/stream.m3u8',
                'hls',
                Quality::HD1
            );

            $timestamp = Carbon::now()->addHours(2)->timestamp;
            $stream->setExpire($timestamp);

            expect($stream->getExpire())->toBeInstanceOf(Carbon::class);
            expect($stream->getExpire()->timestamp)->toBe($timestamp);
        });

        test('should clear expire time', function () {
            $expireTime = Carbon::now()->addHours(1);
            $stream = new Stream(
                'https://example.com/stream.m3u8',
                'hls',
                Quality::HD1,
                false,
                $expireTime
            );

            expect($stream->getExpire())->not->toBeNull();

            $stream->setExpire(null);
            expect($stream->getExpire())->toBeNull();
            expect($stream->isExpired())->toBeFalse();
            expect($stream->getTimeToExpire())->toBeNull();
        });

        test('should calculate time to expire correctly', function () {
            $expireTime = Carbon::now()->addMinutes(30);
            $stream = new Stream(
                'https://example.com/stream.m3u8',
                'hls',
                Quality::HD1,
                false,
                $expireTime
            );

            $timeToExpire = $stream->getTimeToExpire();
            expect($timeToExpire)->toBeGreaterThan(1700); // 约30分钟 - 一些误差
            expect($timeToExpire)->toBeLessThan(1800); // 约30分钟 + 一些误差
        });
    });

    describe('Static Factory Methods with Expire', function () {
        test('should create HLS stream with expire time', function () {
            $expireTime = Carbon::now()->addHours(2);
            $stream = Stream::createHls(
                'https://example.com/stream.m3u8',
                Quality::HD1,
                false,
                $expireTime
            );

            expect($stream->getType())->toBe('hls');
            expect($stream->getExpire())->toBeInstanceOf(Carbon::class);
            expect($stream->getExpire()->timestamp)->toBe($expireTime->timestamp);
        });

        test('should create FLV stream with expire timestamp', function () {
            $timestamp = Carbon::now()->addHours(3)->timestamp;
            $stream = Stream::createFlv(
                'https://example.com/stream.flv',
                Quality::ORIGIN,
                false,
                $timestamp
            );

            expect($stream->getType())->toBe('flv');
            expect($stream->getExpire())->toBeInstanceOf(Carbon::class);
            expect($stream->getExpire()->timestamp)->toBe($timestamp);
        });

        test('should create stream without expire using factory methods', function () {
            $hlsStream = Stream::createHls('https://example.com/stream.m3u8', Quality::HD1);
            $flvStream = Stream::createFlv('https://example.com/stream.flv', Quality::ORIGIN);

            expect($hlsStream->getExpire())->toBeNull();
            expect($flvStream->getExpire())->toBeNull();
        });
    });

    describe('Array Conversion with Expire', function () {
        test('should convert to array with expire information', function () {
            $expireTime = Carbon::now()->addHours(2);
            $stream = new Stream(
                'https://example.com/stream.m3u8',
                'hls',
                Quality::HD1,
                true,
                $expireTime
            );

            $array = $stream->toArray();

            expect($array)->toHaveKey('expire');
            expect($array)->toHaveKey('expire_timestamp');
            expect($array['expire'])->toBe($expireTime->toISOString());
            expect($array['expire_timestamp'])->toBe($expireTime->timestamp);
        });

        test('should convert to array without expire information', function () {
            $stream = new Stream(
                'https://example.com/stream.m3u8',
                'hls',
                Quality::HD1
            );

            $array = $stream->toArray();

            expect($array)->toHaveKey('expire');
            expect($array)->toHaveKey('expire_timestamp');
            expect($array['expire'])->toBeNull();
            expect($array['expire_timestamp'])->toBeNull();
        });

        test('should create from array with expire timestamp', function () {
            $timestamp = Carbon::now()->addHours(1)->timestamp;
            $data = [
                'url' => 'https://example.com/stream.m3u8',
                'type' => 'hls',
                'quality' => Quality::HD1,
                'validated' => true,
                'expire_timestamp' => $timestamp,
            ];

            $stream = Stream::fromArray($data);

            expect($stream->getExpire())->toBeInstanceOf(Carbon::class);
            expect($stream->getExpire()->timestamp)->toBe($timestamp);
        });

        test('should create from array with expire ISO string', function () {
            $expireTime = Carbon::now()->addHours(1);
            $data = [
                'url' => 'https://example.com/stream.m3u8',
                'type' => 'hls',
                'quality' => Quality::HD1,
                'validated' => true,
                'expire' => $expireTime->toISOString(),
            ];

            $stream = Stream::fromArray($data);

            expect($stream->getExpire())->toBeInstanceOf(Carbon::class);
            // 注意：从ISO字符串解析可能会有微小的时间差异
        });

        test('should create from array without expire', function () {
            $data = [
                'url' => 'https://example.com/stream.m3u8',
                'type' => 'hls',
                'quality' => Quality::HD1,
                'validated' => true,
            ];

            $stream = Stream::fromArray($data);

            expect($stream->getExpire())->toBeNull();
        });
    });

    describe('Metadata with Expire', function () {
        test('should include expire information in metadata', function () {
            $expireTime = Carbon::now()->addHours(2);
            $stream = new Stream(
                'https://example.com/stream.m3u8',
                'hls',
                Quality::HD1,
                true,
                $expireTime
            );

            $metadata = $stream->getMetadata();

            expect($metadata)->toHaveKey('expire');
            expect($metadata['expire'])->toHaveKey('timestamp');
            expect($metadata['expire'])->toHaveKey('iso_string');
            expect($metadata['expire'])->toHaveKey('human_readable');
            expect($metadata['expire'])->toHaveKey('is_expired');
            expect($metadata['expire'])->toHaveKey('time_to_expire');

            expect($metadata['expire']['timestamp'])->toBe($expireTime->timestamp);
            expect($metadata['expire']['iso_string'])->toBe($expireTime->toISOString());
            expect($metadata['expire']['is_expired'])->toBeFalse();
            expect($metadata['expire']['time_to_expire'])->toBeGreaterThan(0);
        });

        test('should handle null expire in metadata', function () {
            $stream = new Stream(
                'https://example.com/stream.m3u8',
                'hls',
                Quality::HD1
            );

            $metadata = $stream->getMetadata();

            expect($metadata['expire']['timestamp'])->toBeNull();
            expect($metadata['expire']['iso_string'])->toBeNull();
            expect($metadata['expire']['human_readable'])->toBeNull();
            expect($metadata['expire']['is_expired'])->toBeFalse();
            expect($metadata['expire']['time_to_expire'])->toBeNull();
        });
    });

    describe('Equality and Hash with Expire', function () {
        test('should consider expire time in equality comparison', function () {
            $expireTime1 = Carbon::now()->addHours(1);
            $expireTime2 = Carbon::now()->addHours(2);

            $stream1 = new Stream(
                'https://example.com/stream.m3u8',
                'hls',
                Quality::HD1,
                false,
                $expireTime1
            );

            $stream2 = new Stream(
                'https://example.com/stream.m3u8',
                'hls',
                Quality::HD1,
                false,
                $expireTime1
            );

            $stream3 = new Stream(
                'https://example.com/stream.m3u8',
                'hls',
                Quality::HD1,
                false,
                $expireTime2
            );

            expect($stream1->equals($stream2))->toBeTrue();
            expect($stream1->equals($stream3))->toBeFalse();
        });

        test('should include expire time in hash calculation', function () {
            $expireTime1 = Carbon::now()->addHours(1);
            $expireTime2 = Carbon::now()->addHours(2);

            $stream1 = new Stream(
                'https://example.com/stream.m3u8',
                'hls',
                Quality::HD1,
                false,
                $expireTime1
            );

            $stream2 = new Stream(
                'https://example.com/stream.m3u8',
                'hls',
                Quality::HD1,
                false,
                $expireTime2
            );

            expect($stream1->getHash())->not->toBe($stream2->getHash());
        });
    });

    describe('Edge Cases', function () {
        test('should handle zero timestamp', function () {
            $stream = new Stream(
                'https://example.com/stream.m3u8',
                'hls',
                Quality::HD1,
                false,
                0
            );

            expect($stream->getExpire())->toBeNull();
        });

        test('should handle very large timestamp', function () {
            $largeTimestamp = 2147483647; // 2038年问题的边界
            $stream = new Stream(
                'https://example.com/stream.m3u8',
                'hls',
                Quality::HD1,
                false,
                $largeTimestamp
            );

            expect($stream->getExpire())->toBeInstanceOf(Carbon::class);
            expect($stream->getExpire()->timestamp)->toBe($largeTimestamp);
        });

        test('should handle Carbon exception gracefully', function () {
            // 使用一个可能导致Carbon异常的值
            $invalidTimestamp = -999999999;
            $stream = new Stream(
                'https://example.com/stream.m3u8',
                'hls',
                Quality::HD1,
                false,
                $invalidTimestamp
            );

            expect($stream->getExpire())->toBeNull();
        });
    });

    describe('Immutability', function () {
        test('should be immutable after creation', function () {
            $stream = new Stream(
                'https://example.com/stream.m3u8',
                'hls',
                Quality::HD1
            );

            // Stream对象应该是不可变的，没有setter方法
            expect(method_exists($stream, 'setUrl'))->toBeFalse();
            expect(method_exists($stream, 'setType'))->toBeFalse();
            expect(method_exists($stream, 'setQuality'))->toBeFalse();
        });
    });
});
