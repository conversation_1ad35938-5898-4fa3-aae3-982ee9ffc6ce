<?php

declare(strict_types=1);

use LiveStream\Recording\Pipes\DebugInfoPipe;
use LiveStream\Stream\Stream;
use LiveStream\Enum\Quality;
use Carbon\Carbon;

describe('DebugInfoPipe', function () {

    beforeEach(function () {
        // 重置尝试计数器
        DebugInfoPipe::resetAttemptCounter();
    });

    test('should reset attempt counter correctly', function () {
        // 验证计数器重置功能
        DebugInfoPipe::resetAttemptCounter();

        // 这个测试主要验证重置功能存在且可调用
        expect(true)->toBeTrue();
    });

    test('should handle stream expiration correctly', function () {
        // 测试过期流的处理
        $expiredTime = Carbon::now()->subHours(1);
        $expiredStream = Stream::createHls('http://example.com/stream.m3u8', Quality::ORIGIN, true, $expiredTime);

        expect($expiredStream->isExpired())->toBeTrue();
        expect($expiredStream->getTimeToExpire())->toBe(0);

        // 测试未过期流
        $futureTime = Carbon::now()->addHours(1);
        $validStream = Stream::createHls('http://example.com/stream.m3u8', Quality::ORIGIN, true, $futureTime);

        expect($validStream->isExpired())->toBeFalse();
        expect($validStream->getTimeToExpire())->toBeGreaterThan(0);
    });
});
