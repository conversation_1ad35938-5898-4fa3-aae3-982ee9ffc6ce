<?php

declare(strict_types=1);

use LiveStream\Recording\Pipes\ValidateOptionsPipe;
use LiveStream\Recording\Pipes\StreamValidationPipe;
use LiveStream\Recording\PendingRecorder;
use LiveStream\Recording\RecordrConnector;
use LiveStream\Contracts\PlatformInterface;
use LiveStream\Contracts\LiveInterface;
use LiveStream\Stream\Stream;
use LiveStream\Enum\Quality;
use LiveStream\Enum\StreamType;
use LiveStream\Config\RecordingOptions;
use LiveStream\Exceptions\StreamNotLiveException;
use LiveStream\Exceptions\StreamUnavailableException;

describe('Validation Pipes Integration', function () {

    beforeEach(function () {
        // 创建模拟对象
        $this->mockLive = Mockery::mock(LiveInterface::class);
        $this->mockPlatform = Mockery::mock(PlatformInterface::class);
        $this->mockConnector = Mockery::mock(RecordrConnector::class);
        $this->mockConfig = Mockery::mock(RecordingOptions::class);

        // 设置基本的模拟行为
        $this->mockPlatform->shouldReceive('getLive')->andReturn($this->mockLive);
        $this->mockConnector->shouldReceive('config')->andReturn($this->mockConfig);
        $this->mockConnector->shouldReceive('getDebug')->andReturn(false);

        $this->pendingRecorder = new PendingRecorder($this->mockConnector, $this->mockPlatform);
    });

    afterEach(function () {
        Mockery::close();
    });

    describe('ValidateOptionsPipe', function () {
        test('should pass when live stream is active', function () {
            $this->mockLive->shouldReceive('isLive')->andReturn(true);

            $pipe = new ValidateOptionsPipe();
            $nextCalled = false;

            $next = function ($pendingRecorder) use (&$nextCalled) {
                $nextCalled = true;
                return $pendingRecorder;
            };

            $result = $pipe->handle($this->pendingRecorder, $next);

            expect($nextCalled)->toBeTrue();
            expect($result)->toBe($this->pendingRecorder);
        });

        test('should throw exception when live stream is not active', function () {
            $this->mockLive->shouldReceive('isLive')->andReturn(false);
            $this->mockLive->shouldReceive('getAnchorName')->andReturn('测试主播');

            $pipe = new ValidateOptionsPipe();
            $next = function ($pendingRecorder) {
                return $pendingRecorder;
            };

            expect(fn() => $pipe->handle($this->pendingRecorder, $next))
                ->toThrow(StreamNotLiveException::class);
        });
    });

    describe('StreamValidationPipe', function () {
        test('should validate and set stream successfully', function () {
            // 设置模拟配置
            $this->mockConfig->shouldReceive('getStreamType')->andReturn(StreamType::HLS);

            // 创建模拟流对象，确保所有验证都通过
            $testStream = Mockery::mock(Stream::class);
            $testStream->shouldReceive('validateUrl')->andReturn(true);
            $testStream->shouldReceive('validateFormat')->andReturn(true);
            $testStream->shouldReceive('validateConnection')->andReturn(true);
            $testStream->shouldReceive('getExpire')->andReturn(null);
            $testStream->shouldReceive('isExpired')->andReturn(false);

            // 模拟 Live 对象行为
            $this->mockLive->shouldReceive('findAvailableUrl')
                ->with(StreamType::HLS)
                ->andReturn($testStream);
            $this->mockLive->shouldReceive('getStreamUrl')->andReturn([
                'hls' => ['HD1' => 'http://example.com/stream.m3u8']
            ]);

            $pipe = new StreamValidationPipe();
            $nextCalled = false;

            $next = function ($pendingRecorder) use (&$nextCalled) {
                $nextCalled = true;
                return $pendingRecorder;
            };

            $result = $pipe->handle($this->pendingRecorder, $next);

            expect($nextCalled)->toBeTrue();
            expect($result)->toBe($this->pendingRecorder);
            expect($this->pendingRecorder->getStream())->toBe($testStream);
        });

        test('should throw exception when no stream available', function () {
            $this->mockConfig->shouldReceive('getStreamType')->andReturn(StreamType::HLS);

            $this->mockLive->shouldReceive('findAvailableUrl')
                ->with(StreamType::HLS)
                ->andReturn(null);
            $this->mockLive->shouldReceive('getStreamUrl')->andReturn([]);

            $pipe = new StreamValidationPipe();
            $next = function ($pendingRecorder) {
                return $pendingRecorder;
            };

            expect(fn() => $pipe->handle($this->pendingRecorder, $next))
                ->toThrow(StreamUnavailableException::class);
        });

        test('should throw exception when stream URL validation fails', function () {
            $this->mockConfig->shouldReceive('getStreamType')->andReturn(StreamType::HLS);

            // 创建一个URL验证失败的流
            $invalidStream = Mockery::mock(Stream::class);
            $invalidStream->shouldReceive('validateUrl')->andReturn(false);
            $invalidStream->shouldReceive('getUrl')->andReturn('invalid-url');

            $this->mockLive->shouldReceive('findAvailableUrl')
                ->with(StreamType::HLS)
                ->andReturn($invalidStream);

            $pipe = new StreamValidationPipe();
            $next = function ($pendingRecorder) {
                return $pendingRecorder;
            };

            expect(fn() => $pipe->handle($this->pendingRecorder, $next))
                ->toThrow(StreamUnavailableException::class, 'URL格式无效');
        });

        test('should throw exception when stream format validation fails', function () {
            $this->mockConfig->shouldReceive('getStreamType')->andReturn(StreamType::HLS);

            $invalidStream = Mockery::mock(Stream::class);
            $invalidStream->shouldReceive('validateUrl')->andReturn(true);
            $invalidStream->shouldReceive('validateFormat')->andReturn(false);
            $invalidStream->shouldReceive('getUrl')->andReturn('http://example.com/invalid.txt');

            $this->mockLive->shouldReceive('findAvailableUrl')
                ->with(StreamType::HLS)
                ->andReturn($invalidStream);

            $pipe = new StreamValidationPipe();
            $next = function ($pendingRecorder) {
                return $pendingRecorder;
            };

            expect(fn() => $pipe->handle($this->pendingRecorder, $next))
                ->toThrow(StreamUnavailableException::class, '流格式不匹配');
        });

        test('should throw exception when stream connection validation fails', function () {
            $this->mockConfig->shouldReceive('getStreamType')->andReturn(StreamType::HLS);

            $invalidStream = Mockery::mock(Stream::class);
            $invalidStream->shouldReceive('validateUrl')->andReturn(true);
            $invalidStream->shouldReceive('validateFormat')->andReturn(true);
            $invalidStream->shouldReceive('validateConnection')->andReturn(false);
            $invalidStream->shouldReceive('getUrl')->andReturn('http://example.com/stream.m3u8');

            $this->mockLive->shouldReceive('findAvailableUrl')
                ->with(StreamType::HLS)
                ->andReturn($invalidStream);

            $pipe = new StreamValidationPipe();
            $next = function ($pendingRecorder) {
                return $pendingRecorder;
            };

            expect(fn() => $pipe->handle($this->pendingRecorder, $next))
                ->toThrow(StreamUnavailableException::class, '网络连接失败');
        });
    });

    describe('Integration Test', function () {
        test('should work together without duplicate live status check', function () {
            // 设置模拟
            $this->mockLive->shouldReceive('isLive')->once()->andReturn(true); // 只应该被调用一次
            $this->mockLive->shouldReceive('getAnchorName')->andReturn('测试主播');
            $this->mockConfig->shouldReceive('getStreamType')->andReturn(StreamType::HLS);

            // 创建模拟流对象
            $testStream = Mockery::mock(Stream::class);
            $testStream->shouldReceive('validateUrl')->andReturn(true);
            $testStream->shouldReceive('validateFormat')->andReturn(true);
            $testStream->shouldReceive('validateConnection')->andReturn(true);
            $testStream->shouldReceive('getExpire')->andReturn(null);
            $testStream->shouldReceive('isExpired')->andReturn(false);

            $this->mockLive->shouldReceive('findAvailableUrl')->andReturn($testStream);
            $this->mockLive->shouldReceive('getStreamUrl')->andReturn([
                'hls' => ['HD1' => 'http://example.com/stream.m3u8']
            ]);

            // 创建管道
            $validatePipe = new ValidateOptionsPipe();
            $streamPipe = new StreamValidationPipe();

            // 执行第一个中间件
            $result1 = $validatePipe->handle($this->pendingRecorder, function ($pr) use ($streamPipe) {
                // 执行第二个中间件
                return $streamPipe->handle($pr, function ($pr) {
                    return $pr;
                });
            });

            expect($result1)->toBe($this->pendingRecorder);
            expect($this->pendingRecorder->getStream())->toBe($testStream);
        });
    });
});
