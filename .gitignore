# Composer
/vendor/
composer.lock

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Logs
*.log
logs/

# Cache
cache/
*.cache

# Test coverage
coverage/
.phpunit.result.cache

# Environment
.env
.env.local
.env.*.local

# Build
build/
dist/

# Node.js (for X-Bogus generation)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Temporary files
*.tmp
*.temp
temp/ 

/downloads/
.env
.cursor/
.cloude/
.vccode/